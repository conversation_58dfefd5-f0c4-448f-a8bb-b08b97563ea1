@import './Colors.scss';

@font-face {
    font-family: OpenRunde;
    src: url('../assets/OpenRunde/desktop/OpenRunde-Regular.otf');
}

@font-face {
    font-family: OpenRunde;
    font-weight: bold;
    src: url('../assets/OpenRunde/desktop/OpenRunde-Bold.otf');
}

@font-face {
    font-family: OpenRunde;
    font-weight: 500;
    src: url('../assets/OpenRunde/desktop/OpenRunde-Medium.otf');
}

@font-face {
    font-family: OpenRunde;
    font-weight: 400;
    src: url('../assets/OpenRunde/desktop/OpenRunde-Regular.otf');
}

body,
textarea,
select,
div {
    font-family: OpenRunde;
}

div,
select,
textarea {
    color: $dark-text;
}

button {
    margin: 0px;
    padding: 0px;
    font-family: OpenRunde;
    cursor: pointer;
}

textarea {
    width: 100%;
    height: 96px;
    border: none;
    padding: 16px;
    outline: none;
    resize: none;
    font-size: 15px;
    border-radius: 6px;
    background: $bg-grey;
    box-sizing: border-box;
}

textarea::placeholder {
    color: $grey;
}