@import '../globals/Colors.scss';

.full-screen-view {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
}

.scroll {
    overflow-y: scroll;
}

.scroll::-webkit-scrollbar {
    display: none;
}

.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bold {
    font-weight: 600 !important;
}

.capitalize {
    text-transform: capitalize;
}

.clickable {
    cursor: pointer;
}

.hint-label {
    color: $grey;
    font-size: 15px;
    font-style: italic;
}

.disabled {
    cursor: not-allowed;
    opacity: 0.3;
}

.Toastify__toast-container {
    width: auto !important;
    left: 20px;
    right: 20px;
    transform: translate(0);

    .Toastify__toast {
        width: 100%;
        height: 44px;
        min-height: 44px;
        padding: 14px;
        color: #262D2D;
        border-radius: 8px !important;
        box-sizing: border-box;
        margin: 10px 0px 0px 0px;
        font-size: 13px;
        font-weight: bold;
        font-family: OpenRunde !important;
        background: #E3F8FA;

        .Toastify__toast-icon {
            .check-icon {
                height: 22px;
            }
        }

        .Toastify__close-button {
            top: auto;
            right: 16px;
            opacity: 1;
            color: #262D2D;
        }
    }
}

// react-calendar
.react-datepicker__year-option {
    display: flex !important;
    justify-content: center;
}

.react-datepicker__year-option:hover {
    background: lightgrey;
}

.react-datepicker__navigation--years-upcoming,
.react-datepicker__navigation--years-previous {
    width: 9px !important;
    height: 9px !important;
    top: 2px !important;
    border-color: darkgrey !important;
    border-style: solid !important;
    border-width: 3px 3px 0 0 !important;
    margin: 7.5px auto !important;
    transform: rotate(-45deg);
}

.react-datepicker__navigation--years-previous {
    top: -2px !important;
    transform: rotate(135deg);
}