@import '../../globals/Colors.scss';

.welcome-view {
    display: flex;
    flex-direction: column;

    .toolbar {
        display: flex;
        flex: 0 0 72px;
        padding: 0px 20px;
        align-items: center;
        box-sizing: border-box;
        box-shadow: 0px 1px 0px 0px #E0E0E0;
    }

    .content-frame {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .message {
            margin-top: 96px;
            font-size: 21px;
            font-weight: 500;
        }

        .submessage {
            margin: 24px 0px 88px 0px;
            font-size: 15px;
            font-weight: 400;
            color: $grey;
            width: 405px;
            text-align: center;
        }

        .buttons-frame {
            display: flex;

            .button-frame {
                position: relative;
                width: 610px;
                height: 374px;
                display: flex;
                overflow: hidden;
                padding: 48px 56px;
                border-radius: 32px;
                box-sizing: border-box;
                justify-content: center;
                flex-direction: column;
                background: rgba(0, 131, 144, 0.1);
                box-shadow: 0px 4px 24px 0px #262D2D1F;

                .watermark-icon {
                    position: absolute;
                    top: -20px;
                    height: 420px;
                    right: -37px;
                }

                .title {
                    font-size: 38px;
                    font-weight: 600;
                }

                .members {
                    font-size: 21px;
                    font-weight: 500;
                    margin: 12px 0px 49px 0px;
                }

                .arrow-icon {
                    width: 72px;
                    height: 72px;
                    cursor: pointer;
                    visibility: hidden;
                }
            }

            .button-frame:hover {
                .arrow-icon {
                    visibility: visible;
                }
            }

            .orange {
                margin-right: 40px;
                background: rgba(233, 113, 0, 0.1);

                .title {
                    color: $orange;
                }
            }

            .teal {
                .title {
                    color: $teal;
                }
            }
        }
    }
}