@import '../../../globals/Colors.scss';

.setup-roster-user-view {
    position: relative;
    flex: 1 1;
    display: flex;
    flex-direction: column;

    .step-label {
        color: $grey;
        font-size: 15px;
        font-family: 500;
    }

    .step-content-view {
        flex: 1 1;
        display: flex;
        margin-top: 12px;
        flex-direction: column;

        .sections-frame {
            flex-grow: 1;
            flex-basis: 0;

            .section-frame {
                margin-bottom: 48px;

                .section-header {
                    font-size: 18px;
                    font-weight: 600;
                    margin-bottom: 16px;
                }

                .hint-frame {
                    display: flex;

                    .hint-label {
                        color: $dark-text;
                        font-style: normal;
                        font-size: 15px;
                        font-weight: 400;
                    }

                    .hint-button {
                        font-size: 15px;
                        color: $orange;
                        border: none;
                        margin-left: 5px;
                        background: none;
                    }
                }

                .staff-type-menu {
                    width: 33%;
                    max-width: 400px;
                }

                .form-frame {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;

                    .date-text-field-view,
                    .floating-text-field,
                    .select-menu-view {
                        flex: 0 0 32%;

                        .menu-label,
                        .text-field-header {
                            color: $teal;
                        }

                        .text-field-value {
                            font-weight: 400;
                        }
                    }
                }
            }
        }

        .bottom-toolbar {
            display: flex;
            flex: 0 0 56px;
            align-items: center;
            justify-content: flex-end;

            .disabled {
                cursor: not-allowed;
                opacity: 0.3;
            }
        }
    }
}