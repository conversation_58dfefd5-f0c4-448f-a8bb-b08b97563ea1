@import '../../../globals/Colors.scss';

.scheduler-setup-details-view {
    position: relative;

    .button-frame {
        position: absolute;
        right: 0px;
        top: -72px;
        display: flex;

        .icon-button-view {
            margin-left: 12px;
        }
    }

    .section-frame {
        margin-bottom: 48px;

        .section-header-frame {
            height: 40px;
            display: flex;
            margin-bottom: 24px;
            align-items: center;
            justify-content: space-between;

            .section-header {
                font-weight: 600;
                font-size: 18px;
            }

            .add-button {
                font-size: 15px;
                font-weight: 500;
                color: $orange;
                border: none;
                background: none;
            }
        }

        .form-frame {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;

            .header-value-label-view,
            .floating-text-field,
            .select-menu-view {
                flex: 0 0 32%;
                margin-bottom: 16px;

                .floating-label {
                    color: $grey;
                }
            }
        }

        .insurances-list-view {
            margin: 0px;
            padding: 0px;
            list-style: none;

            .insurance-cell {
                margin-bottom: 24px;

                .cell-label {
                    font-size: 15px;
                    line-height: 19.5px;
                }

                .type {
                    color: $grey;
                }
            }
        }
    }
}