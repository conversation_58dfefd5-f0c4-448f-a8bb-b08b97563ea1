@import '../../../globals/Colors.scss';

.appointments-view {
    display: flex;
    flex-direction: column;

    .toolbar {
        flex-basis: 72px;
        box-shadow: none;

        .toolbar-header {
            font-size: 21px;
            font-weight: 600;
        }

        .right-section {
            .date-range-picker-view {
                margin-right: 12px;
            }

            .icon-button-view {
                position: relative;
            }
        }
    }

    .segmented-button-frame {
        display: flex;
        flex-basis: 60px;
        padding: 0px 20px;
        align-items: center;
        border-bottom: 1px solid $border-grey;
    }

    .table-view {
        flex: 1 1;
        border: none;
        overflow: initial;

        .table-headers {
            border: none;
        }

        .hint-label {
            margin-top: 24px;
        }

        .table-data-cell {
            position: relative;
            border: none !important;
            overflow: hidden;

            .name-frame {
                display: flex;
                align-items: center;

                .avatar {
                    flex: 0 0 32px;
                }
            }

            .full-name {
                margin-left: 12px;
            }

            .status {
                display: flex;
            }

            .flex-frame {
                display: none;
                align-items: center;

                .select-menu-view {
                    height: 34px;
                    margin-bottom: 0px;
                    border-radius: 6px;
                    background: #F7F8F8;

                    .arrow-icon {
                        top: 14px;
                    }
                }
            }

            .profile-details-pane {
                position: absolute;
                opacity: 0;
                z-index: 10;
                min-width: 240px;
                display: flex;
                border-radius: 6px;
                flex-direction: column;
                border: 1px solid $border-grey;

                .name-frame {
                    height: 36px;
                    display: flex;
                    padding: 0px 16px;
                    align-items: center;
                    justify-content: space-between;
                    border-bottom: 1px solid $border-grey;

                    .left-section {
                        display: flex;
                        align-items: center;

                        .avatar {
                            flex: 0 0 22px;
                            font-size: 7px !important;
                        }

                        .full-name {
                            margin-left: 6px;
                        }
                    }

                    .view-profile-button {
                        border: none;
                        color: $teal;
                        font-size: 13px;
                        font-weight: 500;
                        background: none;
                        box-sizing: border-box;
                        text-align: center;
                    }
                }

                .cell-frame {
                    padding: 10.5px 16px;
                    box-sizing: border-box;
                    border-bottom: 1px solid $border-grey;
                }

                .cell-label {
                    font-size: 15px;
                }
            }

            .more-icon {
                width: 28px;
                height: 28px;
                display: none;
                cursor: pointer;
                margin-left: 10px;
                border-radius: 50%;
                align-items: center;
                justify-content: center;
                background: linear-gradient(180deg, #B74F00 0%, #FF7D00 100%);

                .arrow-icon {
                    width: 8px;
                }
            }
        }

        .table-data-cell:hover {
            overflow: inherit;

            .name-frame:hover {
                .profile-details-pane {
                    top: 0px;
                    opacity: 1;
                    background: white;
                }
            }

            .status,
            .appointment-status-view {
                display: none !important;
            }

            .flex-frame {
                display: flex;

                .select-menu-view {
                    flex: 1 1;
                }

                .undo-button {
                    border: none;
                    height: 36px;
                    padding: 5px 10px;
                    margin-left: 10px;
                    border-radius: 4px;
                    font-size: 11px;
                    font-weight: 500;
                    text-transform: uppercase;
                    background: lightgrey;
                    // color: white;
                    // background: linear-gradient(180deg, #FD8205 0%, #E97100 100%);
                }
            }

            .more-icon {
                display: flex;
            }
        }

        .table-data-cell.completed,
        .table-data-cell.no-change {
            background: $bg-grey;
        }

        .table-data-cell.no-change:hover {
            .appointment-status-view {
                display: flex !important;
            }

            .flex-frame {
                display: none !important;
            }

            .more-icon {
                background: linear-gradient(180deg, grey 0%, lightgrey 100%);
            }
        }
    }

    .services-pane {
        flex: 0 0 44px;
        border-top: 1px solid $border-grey;
    }
}

.appointments-pane {
    z-index: 1000;

    .overlay-content-frame {
        position: absolute;
        top: 120px;
        right: 20px;
        display: flex;
        background: white;
        border-radius: 6px;
        flex-direction: column;
        border: 1px solid $border-grey;

        .trigger-button {
            width: 240px;
            height: 36px;
            border: none;
            color: $orange;
            font-size: 15px;
            font-weight: 500;
            background: none;
        }

        .trigger-button.teal {
            color: $teal;
            border-top: 1px solid $border-grey;
        }
    }
}