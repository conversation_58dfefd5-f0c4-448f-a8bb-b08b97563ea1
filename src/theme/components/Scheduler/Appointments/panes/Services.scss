@import '../../../../globals/Colors.scss';

.services-pane {
    display: flex;
    align-items: center;
    justify-content: center;

    .header-frame {
        display: flex;
        align-items: center;

        .services-label {
            font-size: 15px;
        }

        .arrow-icon {
            height: 12px;
            margin-left: 16px;
            transform: rotate(90deg);
        }
    }

    .floating-frame {
        position: absolute;
        display: flex;
        width: 400px;
        // transition: 300ms;
        border-radius: 6px;
        background: white;
        overflow: hidden;
        flex-direction: column;
        align-items: center;

        .header-frame {
            width: 100%;
            flex: 0 0 44px;
            justify-content: center;
            border-bottom: 1px solid $border-grey;

            .arrow-icon {
                transform: rotate(-90deg);
            }
        }

        .services-list-view {
            width: 100%;
            margin: 0px;
            padding: 0px;
            list-style: none;

            .service-cell {
                display: flex;
                height: 59px;
                padding: 13px 16px;
                box-sizing: border-box;
                border-bottom: 1px solid $border-grey;

                .cell-label {
                    font-size: 13px;
                }

                .bold {
                    font-size: 15px;
                }
            }

            .service-cell:last-child {
                border: none;
            }
        }
    }
}

.services-pane.expand {
    .floating-frame {
        bottom: 14px;
        // bottom: 58px;
        border: 1px solid $border-grey;
        box-shadow: 0px 4px 24px 0px #262D2D1F;
    }
}