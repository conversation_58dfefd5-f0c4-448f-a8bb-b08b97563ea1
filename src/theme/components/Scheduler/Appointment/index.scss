 @import '../../../globals/Colors.scss';

 .appointment-view {
     flex: 1 1;
     display: flex;
     flex-direction: column;

     .toolbar {
         padding: 26px 20px;
         box-sizing: border-box;
         height: 72px !important;
         box-shadow: none !important;
     }

     .sections-frame {
         flex: 1 1;
         display: flex;
         padding: 10px 40px;
         box-sizing: border-box;
         flex-direction: column;

         .section-frame {
             margin-bottom: 48px;

             .section-header {
                 font-size: 18px;
                 font-weight: 600;
                 margin-bottom: 20px;
             }

             .form-frame {
                 display: flex;
                 flex-wrap: wrap;
                 justify-content: space-between;

                 .header-value-label-view {
                     flex: 0 0 33%;
                     margin-bottom: 16px;
                 }
             }

             .status-changes-list-view {
                 margin: 0px;
                 padding: 0px;
                 list-style: none;

                 .status-change-cell {
                     position: relative;
                     display: flex;
                     align-items: center;
                     justify-content: space-between;
                     margin-bottom: 16px;

                     .separator {
                         position: absolute;
                         top: 62px;
                         left: 27px;
                         width: 4px;
                         height: 8px;
                         background: $border-grey;
                     }

                     .left-section,
                     .right-section {
                         display: flex;
                         align-items: center;

                         .avatar {
                             margin-right: 10px;
                         }

                         .status-frame {
                             display: flex;
                             flex-direction: column;
                         }

                         .date-bubble-frame {
                             display: flex;
                             flex: 0 0 58px;
                             padding: 15px 12px;
                             background: $bg-grey;
                             border-radius: 50%;
                             align-items: center;
                             box-sizing: border-box;
                             justify-content: center;
                             flex-direction: column;
                             margin-right: 12px;

                             .date-label {
                                 font-size: 10px;
                                 font-weight: 600;
                             }

                             .date-sublabel {
                                 font-size: 13px;
                                 font-weight: 400;
                                 color: $grey;
                             }
                         }
                     }

                     .left-section {
                         flex: 1 1;
                     }

                     .right-section {
                         align-items: flex-end;
                         flex-direction: column;
                         justify-content: center;

                         .flex-frame {
                             display: flex;
                         }

                         .undone {
                             margin-top: 5px;

                             .undone-label {
                                 margin-right: 10px;
                             }
                         }
                     }

                     .cell-label {
                         font-size: 15px;
                         line-height: 19.5px;
                     }
                 }

                 .status-change-cell:last-child {
                     .separator {
                         display: none;
                     }
                 }
             }
         }
     }
 }