@import '../../../../globals/Colors.scss';

.service-queue-view {
    display: flex;
    flex-direction: column;

    .toolbar {
        flex: 0 0 72px;
        box-shadow: none;

        .button-frame {
            display: flex;
            align-items: center;

            .icon-button-view {
                margin-left: 10px;
            }
        }
    }

    .content-frame {
        flex: 1 1;
        display: flex;
        padding: 10px 40px;
        flex-direction: column;

        .section-frame {
            margin-bottom: 48px;

            .section-header {
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 24px;
            }

            .form-frame {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;

                .select-menu-view,
                .member-hint-frame,
                .member-frame,
                .header-value-label-view {
                    flex: 0 0 32%;
                    margin-bottom: 16px;
                }

                .member-hint-frame {
                    height: 46px;
                    display: flex;
                    padding: 0px 16px;
                    align-items: center;
                    border-radius: 6px;
                    box-sizing: border-box;
                    justify-content: space-between;
                    border: 1px solid $border-grey;

                    .hint-label {
                        color: $dark-text;
                        font-size: 15px;
                        font-style: normal;
                    }

                    .search-icon {
                        height: 16px;
                    }
                }

                .member-frame {
                    height: 46px;
                    display: flex;
                    flex-direction: column;

                    .header {
                        color: grey;
                        font-size: 13px;
                        margin-bottom: 3px;
                    }

                    .member-details-frame {
                        display: flex;
                        align-items: center;

                        .name,
                        .select-member-button {
                            font-size: 15px;
                            font-weight: 500;
                            margin: 0px 5px 0px 10px;
                        }

                        .select-member-button {
                            color: $teal;
                            border: none;
                            margin: 0px;
                            padding: 0px;
                            background: none;
                        }
                    }
                }

                .form-textarea {
                    width: 100%;
                }
            }

            .available-times-frame {
                .top-section {
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    border-bottom: 1px solid $border-grey;

                    .segmented-button-view {
                        .segmented-button {
                            .cell-label {
                                padding: 0x 10px;
                            }
                        }
                    }
                }

                .hint-label {
                    margin-top: 24px;
                }

                .times-list-view {
                    padding: 0px;
                    margin: 20px 0px;
                    list-style: none;

                    .time-cell {
                        padding-bottom: 17px;
                    }
                }
            }
        }
    }
}