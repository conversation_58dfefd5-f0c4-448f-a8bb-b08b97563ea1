@import '../../../globals/Colors.scss';

.setup-service-view {
    position: relative;
    flex: 1 1;
    display: flex;
    flex-direction: column;

    .sections-frame {
        flex-grow: 1;
        flex-basis: 0;

        .section-frame {
            margin-bottom: 48px;

            .section-header-frame {
                display: flex;
                margin-bottom: 16px;
                align-items: center;
                justify-content: space-between;

                .add-button {
                    font-size: 15px;
                    font-weight: 500;
                    color: $orange;
                    border: none;
                    background: none;
                }
            }

            .section-header {
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 16px;
            }

            .hint-frame {
                display: flex;

                .hint-label {
                    color: $dark-text;
                    font-style: normal;
                    font-size: 15px;
                    font-weight: 400;
                }

                .hint-button {
                    font-size: 15px;
                    color: $orange;
                    border: none;
                    margin-left: 5px;
                    background: none;
                }
            }

            .form-frame {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;

                .date-text-field-view,
                .floating-text-field,
                .select-menu-view {
                    flex: 0 0 32%;

                    .menu-label,
                    .text-field-header {
                        color: $teal;
                    }

                    .text-field-value {
                        font-weight: 400;
                    }
                }

                .service-textarea {
                    width: 100%;
                    height: 96px;
                    border: none;
                    padding: 16px;
                    outline: none;
                    resize: none;
                    font-size: 15px;
                    border-radius: 6px;
                    background: $bg-grey;
                    box-sizing: border-box;
                }

                .service-textarea::placeholder {
                    color: $grey;
                }
            }

            .table-view {
                .table-data-cell {
                    .name-frame {
                        display: flex;
                        align-items: center;

                        .full-name {
                            margin-left: 10px;
                        }
                    }
                }
            }
        }
    }
}