@import '../../globals/Colors.scss';

.not-found-view {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: flex-start;

    .cloud-icon {
        width: 72px;
        height: 72px;
        margin-top: 72px;
    }

    .header {
        font-size: 32px;
        font-weight: 500;
        color: $dark-text;
        margin: 32px 0px 16px 0px;
    }

    .subheader {
        color: $grey;
        font-size: 15px;
        margin-bottom: 32px;
    }

    .sign-in-button {
        border: none;
        color: $orange;
        font-size: 15px;
        font-weight: 500;
        border-radius: 6px;
        padding: 11px 15px;
        box-sizing: border-box;
        background: linear-gradient(0deg, #ECEEED, #ECEEED), linear-gradient(180deg, #F7F8F8 0%, #DCDFDF 100%);
    }
}