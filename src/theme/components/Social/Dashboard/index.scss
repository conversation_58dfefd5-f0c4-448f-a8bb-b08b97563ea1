@import '../../../globals/Colors.scss';

.dashboard-view {
    display: flex;
    flex-direction: column;

    .toolbar {
        display: flex;
        flex: 0 0 60px;
        padding: 0px 20px;
        align-items: center;
        box-sizing: border-box;
        justify-content: space-between;
        box-shadow: 0px 1px 0px 0px #E0E0E0;

        .platform-button {
            display: flex;
            padding: 10px 16px;
            border-radius: 100px;
            box-sizing: border-box;
            border: 1px solid $border-grey;

            .platform-icon {
                height: 20px;
                margin-right: 12px;
            }

            .platform-label {
                font-size: 15px;
                font-weight: 500;
            }
        }

        .right-section {
            display: flex;
            align-items: center;

            .notifications-frame {
                display: flex;
                align-items: center;
                margin-left: 20px;

                .bell-icon {
                    height: 24px;
                }
            }

            .avatar-frame {
                cursor: pointer;
            }
        }
    }

    .content-frame {
        flex: 1 1;
        display: flex;

        .document-frame {
            position: relative;
            flex: 1 1;
        }

        .floating-button-frame {
            position: absolute;
            right: 0px;
            top: -72px;
            display: flex;

            .icon-button-view {
                margin-left: 12px;
            }

            .disabled {
                cursor: not-allowed;
                opacity: 0.3;
            }
        }
    }
}