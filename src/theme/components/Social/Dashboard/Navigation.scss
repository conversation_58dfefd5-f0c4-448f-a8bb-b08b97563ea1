@import '../../../globals/Colors.scss';

.dashboard-navigation-view {
    flex: 0 0 72px;

    .navigation-list-view {
        margin: 0px;
        padding: 6px;
        list-style: none;
        box-sizing: border-box;

        .navigation-cell {
            width: 88px;
            height: 60px;
            display: flex;
            padding: 3px 0px;
            align-items: center;
            box-sizing: border-box;
            justify-content: center;
            flex-direction: column;

            .nav-icon {
                height: 24px;
            }

            .cell-label {
                color: $grey;
                font-size: 11px;
                margin-top: 4px;
            }
        }

        .navigation-cell.selected {
            border-radius: 8px;
            background: #FFF7EC;

            .cell-label {
                color: $dark-text;
                font-weight: 600;
            }
        }
    }
}