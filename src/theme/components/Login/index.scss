@import '../../globals/Colors.scss';

.login-view {
    display: flex;
    align-items: center;
    background: #F7F8F8;
    justify-content: center;
    flex-direction: column;

    .wellup-logo {
        height: 40px;
    }

    .centered-frame {
        width: 90%;
        max-width: 515px;
        padding: 49px;
        margin-top: 40px;
        border-radius: 12px;
        background: white;
        box-sizing: border-box;
        box-shadow: 0px 8px 48px 0px #0000001F;

        .sign-in-label {
            font-size: 21px;
            font-weight: 600;
            margin-bottom: 24px;
        }

        .form-frame {
            width: 100%;
            margin-bottom: 48px;

            .form-input-frame {
                position: relative;
                height: 48px;
                margin-bottom: 12px;

                .form-input {
                    background: #F7F8F8;
                }
            }

            .error-label {
                color: $red;
                font-size: 11px;
                font-style: italic;
                margin-bottom: 12px;
            }

            .sign-in-button {
                border: none;
                width: 100%;
                height: 48px;
                color: white;
                font-size: 15px;
                font-weight: 500;
                border-radius: 6px;
                background: linear-gradient(180deg, #FD8205 0%, #E97100 100%);
            }

            .sign-in-button.disabled {
                cursor: not-allowed;
                opacity: 0.5;
            }
        }

        .subsection-frame {
            display: flex;
            justify-content: center;

            .subsection-label,
            .click-here-button {
                font-size: 13px;
                font-weight: 500;
            }

            .click-here-button {
                border: none;
                padding: 0px;
                color: $orange;
                background: none;
                margin: 0px 2.5px;
            }
        }
    }
}