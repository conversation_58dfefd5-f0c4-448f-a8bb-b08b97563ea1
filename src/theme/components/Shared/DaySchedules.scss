@import '../../globals/Colors.scss';

.day-schedules-list-view {
    padding: 0px;
    list-style: none;
    margin: 24px 0px 48px 0px;

    .day-schedule-cell {
        margin-bottom: 1px;
        flex-direction: column;
        border-bottom: 1px solid $border-grey;

        .scheduler-header-frame {
            width: 100%;
            height: 52px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .add-button {
                border: none;
                font-size: 13px;
                font-weight: bold;
                color: $orange;
                background: none;
                text-transform: capitalize;
            }
        }

        .schedule-frame {
            display: flex;
            flex-direction: column;
            margin: 13px 30px 24px 30px;

            .range-slider {
                height: 80px;

                .track {
                    position: relative;
                    top: 49px;
                    height: 4px;
                    background: #E9EBEB;

                    .remove-button {
                        position: relative;
                        top: 5px;
                        width: 92.5%;
                        color: $red;
                        margin: 0px;
                        padding: 0px;
                        border: none;
                        font-size: 11px;
                        font-weight: 500;
                        background: none;
                        padding: 10px !important;
                        // margin-left: -22.5px;
                    }
                }

                .track.segment {
                    // z-index: 2;
                    background: $teal;
                }

                .thumb-active {
                    outline: none;
                }

                .thumb {
                    position: relative;
                    // top: 14px;
                    top: 43px;
                    outline: none;
                    display: flex;
                    cursor: pointer;
                    flex-direction: column;
                    justify-content: center;

                    .date {
                        position: absolute;
                        top: -30px;
                        left: -19px;
                        // position: relative;
                        // left: -19px;
                        color: white;
                        font-size: 10px;
                        padding: 5px 5px;
                        background: #191819;
                        border-radius: 6px;
                        margin-bottom: 6px;
                        box-sizing: border-box;
                    }

                    .dot {
                        position: relative;
                        left: -4px;
                        top: -2.5px;
                        width: 20px;
                        height: 20px;
                        background: $teal;
                        border-radius: 50%;
                        box-sizing: border-box;
                        border: 3px solid white;
                        box-shadow: 0px 2px 8px 0px #00000033;
                    }
                }

                // .thumb.end {
                //     z-index: 5;

                //     .date {
                //         left: 19px;
                //     }

                //     .dot {
                //         left: 34px;
                //     }
                // }
            }

            .segment-slots-frame {
                margin-top: 20px;

                .section-header {
                    font-size: 15px;
                    font-weight: bold;
                }

                .day-schedules-concurrency-list-view {
                    margin: 0px;
                    padding: 0px;
                    list-style: none;

                    .day-schedule-cell {
                        max-width: 300px;
                        margin: 0px;
                        display: flex;
                        padding: 10px 0px;
                        align-items: center;
                        box-sizing: border-box;
                        border-bottom: 1px solid lightgrey;

                        .date {
                            width: 150px;
                            font-size: 13px;
                        }

                        .remove-button {
                            margin-left: 20px;
                            border: none;
                            font-weight: bold;
                            color: #A73575;
                            background: none;
                        }
                    }
                }
            }
        }
    }
}