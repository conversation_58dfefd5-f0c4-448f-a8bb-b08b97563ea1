@import '../../globals/Colors.scss';

.floating-text-field {
    position: relative;
    height: 48px;
    margin-bottom: 12px;

    .floating-input {
        width: 100%;
        height: 100%;
        border: none;
        outline: none;
        padding: 14px 16px 0px 16px;
        font-size: 15px;
        font-weight: 500;
        color: #262D2D;
        border-radius: 6px;
        background: #F7F8F8;
        box-sizing: border-box;
    }

    .floating-label {
        position: absolute;
        top: 14.5px;
        left: 16px;
        color: #747A7A;
        pointer-events: none;
        transition: all 0.2s ease-in-out;
    }

    .floating-icon {
        position: absolute;
        top: 12px;
        right: 16px;
    }
}

.floating-text-field.focused {
    .floating-label {
        top: 6px;
        left: 16px;
        font-size: 13px;
        color: $teal;
    }
}