@import '../../globals/Colors.scss';

.loading-pane {
    display: flex;
    z-index: 101;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.1);

    .loader {
        border: 4px solid white;
        border-top: 4px solid $teal;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
}