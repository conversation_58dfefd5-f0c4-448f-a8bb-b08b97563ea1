@import '../../globals/Colors.scss';

.segmented-button-view {
    display: flex;
    margin: 0px;
    padding: 0px;
    list-style: none;
    overflow: hidden;
    border-radius: 6px;
    background: linear-gradient(0deg, #ECEEED, #ECEEED), linear-gradient(180deg, #F7F8F8 0%, #DCDFDF 100%);

    .segmented-button {
        .cell-label {
            height: 36px;
            display: flex;
            font-size: 13px;
            padding: 0px 24px;
            color: $dark-text;
            align-items: center;
            box-sizing: border-box;
            justify-content: center;
        }
    }

    .segmented-button.selected {
        background: linear-gradient(180deg, #404848 0%, #868B8B 100%);

        .cell-label {
            color: white;
        }
    }
}