@import '../../globals/Colors.scss';

.select-menu-view {
    position: relative;
    height: 46px;
    display: flex;
    overflow: hidden;
    align-items: center;
    margin-bottom: 12px;
    border-radius: 8px;
    border: 1px solid $border-grey;

    .menu-label {
        position: absolute;
        z-index: 1;
        top: 8px;
        left: 16px;
        color: $grey;
        font-size: 13px;
        line-height: 100%;
    }

    .select-menu {
        position: relative;
        z-index: 1;
        outline: none;
        width: 100%;
        height: 100%;
        border: none;
        padding: 0px 16px 0px 16px;
        font-size: 15px;
        // font-weight: 500;
        appearance: none;
        background: transparent;
    }

    .select-menu.floating {
        padding: 15px 0px 0px 16px;
    }

    .arrow-icon {
        position: absolute;
        z-index: 0;
        right: 16px;
        width: 12px;
        height: 6px;
    }
}