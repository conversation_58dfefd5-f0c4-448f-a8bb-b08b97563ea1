@import '../../globals/Colors.scss';

.role-selector-view {
    display: flex;
    width: 400px;
    height: 44px;
    padding: 6px;
    border-radius: 8px;
    align-items: center;
    box-sizing: border-box;
    justify-content: space-around;
    border: 1px solid $border-grey;

    .middle-section {
        flex: 1 1;
        margin: 0px 8px;

        .name {
            font-size: 13px;
            line-height: 16.9px;
            color: $dark-text;
        }

        .role {
            font-size: 13px;
            line-height: 14.3px;
            color: $grey;
        }

        .role.empty {
            font-style: italic;
        }
    }

    .arrow-icon {
        width: 12px;
        height: 6px;
        padding: 10px;
    }
}

.role-selection-menu {
    position: relative;
    top: 25px;
    z-index: 1000;

    .background {
        position: fixed;
    }

    .roles-list-view {
        position: absolute;
        right: 0px;
        // top: 55px;
        width: 400px;
        margin: 0px;
        padding: 0px;
        list-style: none;
        overflow: hidden;
        border-radius: 8px;
        background: white;
        box-shadow: 0px 4px 24px 0px #262D2D1F;

        .role-cell {
            height: 32px;
            display: flex;
            font-size: 13px;
            font-weight: 400;
            padding: 0px 16px;
            align-items: center;

            .selected {
                color: $teal;
                margin-left: 2.5px;
            }
        }

        .role-cell:hover {
            background: #E9EBEB;
        }
    }
}