@import '../../globals/Colors.scss';

.table-view {
    display: flex;
    overflow: hidden;
    border-radius: 4px;
    flex-direction: column;
    border: 1px solid $border-grey;

    .table-headers {
        display: flex;
        margin: 0px;
        flex: 0 0 32px;
        list-style: none;
        padding: 0px 20px;
        align-items: center;
        border-bottom: 1px solid $border-grey;

        .table-header {
            color: $grey;
            display: flex;
            font-size: 13px;
            font-weight: 600;
            align-items: center;

            .sort-icon {
                height: 12px;
                margin-left: 6px;
            }
        }
    }

    .hint-label {
        display: flex;
        height: 44px;
        padding: 0px 20px;
        align-items: center;
        box-sizing: border-box;
        justify-content: center;
    }

    .table-data-list-view {
        margin: 0px;
        padding: 0px;
        list-style: none;

        .table-data-cell {
            height: 44px;
            display: flex;
            padding: 0px 20px;
            align-items: center;
            box-sizing: border-box;
            border-bottom: 1px solid $border-grey;

            .cell-label {
                font-size: 15px;
                font-weight: 500;
            }
        }

        .table-data-cell:last-child {
            border: none;
        }
    }
}