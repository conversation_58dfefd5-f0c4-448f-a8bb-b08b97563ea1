@import '../../globals/Colors.scss';

.date-range-picker-view {
    display: flex;
    height: 40px;
    border-radius: 6px;
    border: 1px solid $border-grey;

    .arrow-icon {
        width: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 8px;
        }
    }

    .arrow-icon.forward {
        img {
            transform: rotate(180deg);
        }
    }

    .date-frame {
        position: relative;
        display: flex;
        padding: 0px 16px;
        align-items: center;
        box-sizing: border-box;
        border-left: 1px solid $border-grey;
        border-right: 1px solid $border-grey;

        .date-text-field-view {
            position: absolute;
            top: 0px;
            left: 0px;
            right: 0px;
            bottom: 0px;
            height: 100%;
            margin: 0px;

            .text-field-frame {
                opacity: 0;
            }
        }

        .date-label {
            font-size: 15px;
            font-weight: 500;
            margin-right: 24px;
        }

        .calendar-icon {
            height: 24px;
        }
    }
}