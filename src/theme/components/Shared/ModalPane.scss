@import '../../globals/Colors.scss';

.modal-pane {
    position: fixed;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    z-index: 100;
    // background: rgba(0,0,0,0.3);
    background: rgba(25, 24, 25, 0.5);

    .background {
        position: absolute;
        top: 0px;
        left: 0px;
        right: 0px;
        bottom: 0px;
        z-index: 0;
    }

    .modal-pane-content {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 1;
        overflow: hidden;
        border-radius: 8px;
        min-width: 450px;
        min-height: 350px;
        background: white;
        transition: 0.3s;
        transform: translate(-50%, -50%);
        box-shadow: 0px 8px 48px 0px #0000001F;

        .modal-header-frame {
            position: relative;
            display: flex;
            min-height: 60px;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            border-bottom: 1px solid $border-grey;

            .modal-header {
                font-size: 18px;
                font-weight: 500;
                line-height: 27px;
            }

            .modal-sub-header {
                font-size: 15px;
                font-weight: 400;
                line-height: 22.5px;
            }

            .x-icon {
                position: absolute;
                top: 12px;
                right: 16px;
                padding: 3px;
                height: 30px;
                cursor: pointer;
            }
        }

        .modal-content-frame {
            flex: 1 1;
        }

        .modal-bottom-toolbar-frame {
            display: flex;
            flex: 0 0 59px;
            padding: 0px 12px;
            align-items: center;
            box-sizing: border-box;
            justify-content: flex-end;
            border-top: 1px solid $border-grey;
        }
    }

    .modal-pane-content.slide-down {
        transform: translate(-50%, 200%);
    }
}