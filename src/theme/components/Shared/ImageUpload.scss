@import '../../globals/Colors.scss';

.image-upload-view {
    position: relative;
    border-radius: 50%;
    width: 192px;
    height: 192px;
    overflow: hidden;
    margin-bottom: 20px;

    .file-icon {
        width: 100%;
        height: 100%;
    }

    .overlay-frame {
        position: absolute;
        top: 0px;
        left: 0px;
        right: 0px;
        bottom: 0px;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        background: rgba(38, 45, 45, 0.15);
        display: none;

        .remove-button,
        .choose-new-button {
            border: none;
            color: red;
            padding: 11px 15px;
            background: #FFF;
            font-size: 13px;
            font-weight: 500;
            border-radius: 6px;
            box-sizing: border-box;
        }

        .choose-new-button {
            color: white;
            background: $orange;
        }

        .choose-new-frame {
            position: relative;
            margin-top: 6px;

            .file-input {
                opacity: 0;
            }
        }
    }

    .add-photo-frame {
        position: relative;
        width: 192px;
        height: 192px;
        border-radius: 50%;
        background: #F7F8F8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .file-input {
            opacity: 0;
        }

        .camera-icon {
            height: 72px;
        }

        .add-photo-label {
            color: $orange;
            font-size: 15px;
            font-weight: 600;
        }
    }
}

.image-upload-view:hover {
    .overlay-frame {
        display: flex;
    }
}