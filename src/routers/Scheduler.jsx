import { Route } from "react-router"

import Account from '../components/Account'
import Appointments from "../components/Scheduler/Appointments"
import Appointment from "../components/Scheduler/Appointment"
import AppointmentCreate from "../components/Scheduler/Appointment/Create"
import Dashboard from "../components/Scheduler/Dashboard"
import ServiceQueue from "../components/Scheduler/Service/Queue"
import ServiceQueueCreate from "../components/Scheduler/Service/Queue/Create"
import Setup from "../components/Scheduler/Setup"

// const SchedulerRouter = (props) => {
//     return (
//         <Route>
//             {/* <Route path=':type' element={<Dashboard />} /> */}
//             {/* <Route path=':type/:tab' element={<Dashboard />} /> */}
//             {/* <Route path=':type/:tab/:id' element={<Dashboard />} /> */}
//         </Route>
//     )
// }

const SchedulerRouter = (props) =>
    <Route path='/dashboard' element={<Dashboard />}>
        <Route path='account' element={<Account />} />
        <Route path='appointments' element={<Appointments />} />
        <Route path='appointments/create' element={<AppointmentCreate />} />
        <Route path='appointments/:appt' element={<Appointment />} />
        <Route path='appointments/queue/create' element={<ServiceQueueCreate />} />
        <Route path='reporting' element={<EmptyView message='Reporting coming soon...' />} />
        <Route path='setup/:tab?/:id?' element={<Setup />} />
        <Route path='services/:service/queue/:appt' element={<ServiceQueue />} />
    </Route>

const EmptyView = ({ message }) => {
    return (
        <div
            className='hint-label full-screen-view'
            style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
            }}
        >{message}</div>
    )
}
export default SchedulerRouter