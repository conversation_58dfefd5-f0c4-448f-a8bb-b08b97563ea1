import { useEffect, useState } from "react"
import { Routes, Route, useLocation, useNavigate } from "react-router"
import { useQuery } from '@apollo/client'

import <PERSON>ie from 'js-cookie'

import { selectedRole, token } from '../helpers/Cookie'
import { UserContext } from '../context/User'
import { RoleContext } from '../context/Role'

import SessionQuery from '../graphql/queries/Session'

import LoadingPane from "../components/Shared/LoadingPane"

import Login from '../components/Login'
import NotFound from '../components/NotFound'
// import Welcome from '../components/Welcome' // Commented out - no longer needed for platform selection

import SchedulerRouter from "./Scheduler"
// import SocialRouter from "./Social" // Commented out - social platform disabled

const App = () => {
    const [onload, setLoading] = useState(true)
    const [role, setRole] = useState(selectedRole)

    const location = useLocation()
    const navigate = useNavigate()

    // Always use scheduler platform - no platform selection needed

    const { data, loading, error } = useQuery(SessionQuery)

    useEffect(() => {
        let session = token,
            routePath = (location.pathname === '/')

        if (error && session) {
            // clear token and require login
            Cookie.remove(process.env.REACT_APP_COOKIE_NAME)
            Cookie.remove(`${process.env.REACT_APP_COOKIE_NAME}-platform`)
            window.location.reload()
        } else if (session && data) {
            // Auto-set scheduler platform cookie if not present
            if (!Cookie.get(`${process.env.REACT_APP_COOKIE_NAME}-platform`)) {
                Cookie.set(`${process.env.REACT_APP_COOKIE_NAME}-platform`, 'scheduler')
            }

            // Auto-set default role if none selected
            if (!selectedRole) {
                const providerRole = data.me.roles.find(o => o.role === 'provider')
                if (providerRole) {
                    Cookie.set(`${process.env.REACT_APP_COOKIE_NAME}-role`, JSON.stringify(providerRole))
                }
            }

            // Always redirect to dashboard for authenticated users
            if (location.pathname === '/') navigate('/dashboard')

            // cookie is present, token is verified
            setTimeout(() => setLoading(false), 250)
        } else if (!session && !routePath) {
            // cookie not present and trying to access nested route
            navigate('/')
            setLoading(false)
        } else {
            setLoading(false)
        }
    }, [data, error, navigate, location.pathname])

    if (onload || loading) return <LoadingPane />

    return (
        <UserContext.Provider value={data?.me}>
            <RoleContext.Provider value={{ role: role ? ((typeof role === 'string') ? JSON.parse(role) : role) : null, setRole }}>
                <Routes>
                    <Route index element={<Login />} />
                    {/* <Route path='/welcome' element={<Welcome />} /> */} {/* Commented out - no platform selection needed */}
                    <Route path="/error" element={<NotFound />} />
                    <Route path="*" element={<NotFound />} />

                    {/* Always use scheduler platform - social platform commented out */}
                    {SchedulerRouter(data)}
                    {/* {(platform === 'social') && SocialRouter(data)} */}
                </Routes>
            </RoleContext.Provider>
        </UserContext.Provider>
    )
}

export default App