import { useEffect, useState } from "react"
import { Routes, Route, useLocation, useNavigate } from "react-router"
import { useQuery } from '@apollo/client'

import <PERSON>ie from 'js-cookie'

import { selectedRole, token } from '../helpers/Cookie'
import { UserContext } from '../context/User'
import { RoleContext } from '../context/Role'

import SessionQuery from '../graphql/queries/Session'

import LoadingPane from "../components/Shared/LoadingPane"

import Login from '../components/Login'
import NotFound from '../components/NotFound'
import Welcome from '../components/Welcome'

import SchedulerRouter from "./Scheduler"
import SocialRouter from "./Social"

const App = () => {
    const [onload, setLoading] = useState(true)
    const [role, setRole] = useState(selectedRole)

    const location = useLocation()
    const navigate = useNavigate()

    // used for app routing
    const platform = Cookie.get(`${process.env.REACT_APP_COOKIE_NAME}-platform`)

    const { data, loading, error } = useQuery(SessionQuery)

    useEffect(() => {
        let session = token,
            routePath = (location.pathname === '/')

        if (error && session) {
            // clear token and require login
            Cookie.remove(process.env.REACT_APP_COOKIE_NAME)
            Cookie.remove(`${process.env.REACT_APP_COOKIE_NAME}-platform`)
            window.location.reload()
        } else if (session && data) {
            if (!platform) navigate('/welcome')
            else if (location.pathname === '/') navigate(platform ? '/dashboard' : '/welcome') // pathname is default route, auto re-direct

            // cookie is present, token is verified
            setTimeout(() => setLoading(false), 250)
        } else if (!session && !routePath) {
            // cookie not present and trying to access nested route
            navigate('/')
            setLoading(false)
        } else {
            setLoading(false)
        }
    }, [data, error, navigate, location.pathname, platform])

    if (onload || loading) return <LoadingPane />

    return (
        <UserContext.Provider value={data?.me}>
            <RoleContext.Provider value={{ role: (typeof role === 'string') ? JSON.parse(role) : role, setRole }}>
                <Routes>
                    <Route index element={<Login />} />
                    <Route path='/welcome' element={<Welcome />} />
                    <Route path="/error" element={<NotFound />} />
                    <Route path="*" element={<NotFound />} />

                    {(platform === 'scheduler') && SchedulerRouter(data)}
                    {(platform === 'social') && SocialRouter(data)}
                </Routes>
            </RoleContext.Provider>
        </UserContext.Provider>
    )
}

export default App