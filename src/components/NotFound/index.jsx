import { useNavigate } from 'react-router'

const NotFound = () => {
    const navigate = useNavigate()

    return (
        <div className='not-found-view full-screen-view'>
            <img
                className='cloud-icon'
                alt='cloud=icon'
                src={require('../../theme/assets/cloud-icon.png')}
            />

            <div className='header'>Unauthorized Access</div>

            <div className='subheader'>Sorry, you are not authorized to access this page.</div>

            <button className='sign-in-button' onClick={() => navigate('/')}>Sign In</button>
        </div>
    )
}

export default NotFound