import { useEffect, useState } from "react"
import { useMutation, useQuery } from "@apollo/client"

import AccountQuery from "../../graphql/queries/components/Account"
import UserUpdateMutation from "../../graphql/mutations/User/Update"

import { logout } from "../../helpers/Logout"

import useConstants from "../../hooks/useConstants"

import Avatar from '../Shared/Avatar'
import Breadcrumb from "../Shared/Breadcrumb"
import FloatingTextField from '../Shared/FloatingTextField'
import IconButton from "../Shared/IconButton"
import ImageUpload from "../Shared/ImageUpload"
import LoadingPane from "../Shared/LoadingPane"
import SelectMenu from "../Shared/SelectMenu"

const Account = () => {
    const [onload, setOnload] = useState(true)
    const [requestLoading, setRequestLoading] = useState(false)
    const [user, setUser] = useState({})
    const [edit, setEdit] = useState(false)

    const { genders } = useConstants()

    // graphQL
    const { data, loading } = useQuery(AccountQuery)
    const [userUpdate] = useMutation(UserUpdateMutation)

    const breadcrumbButtons = edit ?
        [
            { title: 'My Account' },
            { title: 'Edit Profile' }
        ]
        :
        [
            { title: 'My Account' }
        ]

    useEffect(() => {
        if (onload && !loading) {
            setUser(data.me)
            setOnload(false)
        }
    }, [onload, loading, data])

    function handleChange(key, value) {
        setUser({
            ...user,
            [key]: value
        })
    }

    function save() {
        const { id, email, file, firstName, lastName, phone } = user
        const data = {
            id,
            firstName,
            lastName
        }

        if (email) data.emails = [{ label: 'main', address: email }]
        if (phone) data.phones = [{ label: 'main', number: phone }]
        if (file) data.attachments = [{ label: 'profile_picture', base64: file }]

        setRequestLoading(true)

        userUpdate({
            variables: {
                input: data
            }
        }).then(response => {
            const { errors, user } = response.data.userUpdate

            setRequestLoading(false)

            if (user) {
                setEdit(false)
                setUser(user)
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            setRequestLoading(false)
            window.alert(error)
        })
    }

    if (onload) return <LoadingPane />

    const email = user?.emails.find(o => o.label === 'main')
    const phone = user?.phones.find(o => o.label === 'main')
    const gender = user?.latestData.find(o => o.key === 'person.gender')

    return (
        <div className='account-view full-screen-view scroll'>
            {requestLoading && <LoadingPane />}

            <div className='toolbar'>
                <Breadcrumb
                    buttons={breadcrumbButtons}
                />

                {edit ?
                    <div className='right-section'>
                        <IconButton
                            icon={require('../../theme/assets/check-icon-white.png')}
                            iconHeight={9.17}
                            background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                            onClick={save}
                            title={{
                                color: 'white',
                                label: 'Save'
                            }}
                        />
                    </div>
                    :
                    <div className='right-section'>
                        <IconButton
                            icon={require('../../theme/assets/lock-icon-teal.png')}
                            onClick={() => { }}
                            title={{
                                color: '#008390',
                                label: 'Update Password'
                            }}
                        />

                        <IconButton
                            className='sign-out-button'
                            icon={require('../../theme/assets/logout-icon-red.png')}
                            onClick={logout}
                            title={{
                                color: '#E42B57',
                                label: 'Sign Out'
                            }}
                        />
                    </div>
                }
            </div>

            <div className='content-frame'>
                {edit ?
                    <div className='edit-account-details-frame'>
                        <div className='section-header'>Profile Details</div>

                        <ImageUpload
                            onChange={file => handleChange('file', file)}
                            user={user}
                        />

                        <div className='form-frame'>
                            <FloatingTextField
                                label='First name'
                                onChange={text => handleChange('firstName', text)}
                                value={user?.firstName}
                            />

                            <FloatingTextField
                                label='Last name'
                                onChange={text => handleChange('lastName', text)}
                                value={user?.lastName}
                            />

                            <FloatingTextField
                                label='Email address'
                                onChange={text => handleChange('email', text)}
                                value={user?.email ?? email?.address}
                            />

                            <FloatingTextField
                                label='Phone'
                                onChange={text => handleChange('phone', text)}
                                value={user?.phone ?? phone?.number}
                            />

                            <SelectMenu
                                label='Gender'
                                options={genders}
                                onChange={value => handleChange('gender', value)}
                                placeholder='Select Gender'
                                value={user?.gender ?? gender?.values[0]}
                            />

                            <FloatingTextField
                                label='Languages Spoken'
                                onChange={text => console.log(text)}
                            />
                        </div>
                    </div>
                    :
                    <div className='account-details-frame'>
                        <Avatar
                            width={240}
                            user={user}
                        />

                        <div className='right-section'>
                            <div className='header-frame'>
                                <div className='name'>{`${user.firstName} ${user.lastName}`}</div>

                                <IconButton
                                    className='sign-out-button'
                                    icon={require('../../theme/assets/edit-icon-orange.png')}
                                    onClick={() => setEdit(true)}
                                    title={{
                                        color: '#E97100',
                                        label: 'Edit Profile'
                                    }}
                                />
                            </div>

                            <ul className='account-details-list-view'>
                                <li className='account-details-cell'>
                                    <div className='cell-label key'>Gender:</div>
                                    <div className='cell-label value'>Male</div>
                                </li>

                                <li className='account-details-cell'>
                                    <div className='cell-label key'>Languages:</div>
                                    <div className='cell-label value'>English, Spanish, Creole</div>
                                </li>

                                <li className='account-details-cell'>
                                    <div className='cell-label key'>Email:</div>
                                    <div className='cell-label value'>{email?.address ?? '-'}</div>
                                </li>

                                <li className='account-details-cell'>
                                    <div className='cell-label key'>Phone:</div>
                                    <div className='cell-label value'>{phone?.number ?? '-'}</div>
                                </li>
                            </ul>
                        </div>
                    </div>
                }
            </div>
        </div>
    )
}

export default Account