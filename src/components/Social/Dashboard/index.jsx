import { useContext, useEffect } from "react"
import { useNavigate, useParams } from "react-router"

import { selectedRole } from '../../../helpers/Cookie'

import { RoleContext } from '../../../context/Role'
import { UserContext } from '../../../context/User'

import DashboardNavigation from "./Navigation"

import Account from "../../Account"

import RoleSelector from "../../Shared/RoleSelector"

const Dashboard = (props) => {
    const user = useContext(UserContext)
    const { role } = useContext(RoleContext)

    const navigate = useNavigate()
    const params = useParams()

    useEffect(() => {
        if (params.type === 'dashboard') navigate('/tasks')
        // else if (role !== selectedRole) window.location.reload()
    }, [role])

    function renderContentView() {
        const route = params.type
        const Todo = (
            <div style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                textTransform: 'capitalize',
                color: 'grey',
                fontStyle: 'italic'
            }}>{`${route} coming soon...`}</div>
        )
        const Components = {
            account: Account,
            chats: Todo,
            households: Todo,
            members: Todo,
            navigators: Todo,
            networks: Todo,
            reporting: Todo,
            tasks: Todo,
            teams: Todo,
        }

        if (Components[route]) {
            const Component = Components[route]

            return (typeof Component === 'object') ? Todo : <Component />
        } else {
            return Todo
        }
    }

    return (
        <div className='dashboard-view full-screen-view'>
            <div className='toolbar'>
                <div className='platform-button clickable' onClick={() => navigate('/welcome')}>
                    <img
                        alt='platform-icon'
                        className='platform-icon'
                        src={require('../../../theme/assets/platform-icon.png')}
                    />

                    <div className='platform-label'>Social</div>
                </div>

                <div className='right-section'>
                    <RoleSelector user={user} />

                    <div className='notifications-frame'>
                        <img
                            className='bell-icon'
                            alt='bell-icon'
                            src={require('../../../theme/assets/bell-icon.png')}
                        />
                    </div>
                </div>
            </div>

            <div className='content-frame'>
                <DashboardNavigation />

                <div className='document-frame'>
                    {renderContentView()}
                </div>
            </div>
        </div >
    )
}

export default Dashboard