import { useParams, useNavigate } from "react-router"

const buttons = [
    { name: 'Tasks', route: 'tasks' },
    { name: 'Households', route: 'households' },
    { name: 'Members', route: 'members' },
    { name: 'Teams', route: 'teams' },
    { name: 'Navigators', route: 'navigators' },
    { name: 'Networks', route: 'networks' },
    { name: 'Chats', route: 'chats' },
    { name: 'Reporting', route: 'reporting' },
]

const DashboardNavigation = () => {
    const navigate = useNavigate()
    const params = useParams()

    return (
        <div className='dashboard-navigation-view'>
            <ul className='navigation-list-view'>
                {buttons.map(button => {
                    const selected = (params.type === button.route)

                    return (
                        <li
                            key={button.route}
                            className={`navigation-cell clickable ${selected ? 'selected' : ''}`}
                            onClick={() => navigate(`/${button.route}`)}
                        >
                            <img
                                alt='nav-icon'
                                className='nav-icon'
                                src={require(`../../../theme/assets/navigation/${button.route}-${selected ? 'icon-selected' : 'icon'}.png`)}
                            />

                            <div className='cell-label'>{button.name}</div>
                        </li>
                    )
                })}
            </ul>
        </div>
    )
}

export default DashboardNavigation