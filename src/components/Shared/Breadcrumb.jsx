import PropTypes from 'prop-types'

const Breadcrumb = ({ buttons }) => {
    return (
        <ul className='breadcrumb-list-view'>
            {buttons.map((button, i) => {
                return (
                    <li
                        className={`breadcrumb-cell ${(button?.onClick) ? 'clickable' : ''}`}
                        key={i}
                        onClick={() => {
                            if (button?.onClick) button.onClick()
                        }}
                        style={{
                            fontWeight: (buttons?.length === 1) ? 500 : (i !== 0 ? 500 : 400)
                        }}
                    >
                        {`${button.title}${i < (buttons.length - 1) ? ' >' : ''}`}
                    </li>
                )
            })}
        </ul>
    )
}

Breadcrumb.propTypes = {
    buttons: PropTypes.array.isRequired
}

export default Breadcrumb