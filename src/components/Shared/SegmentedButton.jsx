import PropTypes from "prop-types"

const SegmentedButton = ({ buttons, onClick }) => {
    return (
        <ul className='segmented-button-view clickable'>
            {buttons.map((button, i) => {
                const { selected, title } = button

                return (
                    <li
                        className={`segmented-button ${selected ? 'selected' : ''}`}
                        key={i}
                        onClick={() => onClick(button)}
                    >
                        <div className='cell-label'>{title}</div>
                    </li>
                )
            })}
        </ul>
    )
}

SegmentedButton.propTypes = {
    buttons: PropTypes.array.isRequired,
    onClick: PropTypes.func
}

export default SegmentedButton