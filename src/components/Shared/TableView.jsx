import { useState } from 'react'
import { orderBy } from 'lodash'

const TableView = ({ data, headers, keys, onCellClick }) => {
    const [sortKey, setSortKey] = useState()
    const [sortDir, setSortDir] = useState('asc')

    const arr = sortKey ? orderBy(data, [sortKey], [sortDir]) : data

    function handleSort(i) {
        if (!data?.length) return

        const obj = data[0]
        const param = keys[i]
        const columnObject = obj?.[param]

        if (typeof columnObject === 'object') {
            /* 
                this handles cell customization outside normal string values. 
                make sure to use { sort: String, component: <div/> } to get intended functionality.
            */
            if (sortKey === columnObject.sort) {
                setSortDir((sortDir === 'asc') ? 'desc' : 'asc')
            } else {
                setSortKey(columnObject.sort)
            }
        } else {
            if (sortKey === keys[i]) {
                setSortDir((sortDir === 'asc') ? 'desc' : 'asc')
            } else setSortKey(keys[i])
        }
    }
    return (
        <div className='table-view'>
            <ul className='table-headers'>
                {headers.map((header, i) => {
                    return (
                        <li
                            className='table-header clickable'
                            key={i}
                            style={{
                                flex: header?.width ? '' : '1 1',
                                width: header?.width ?? 'auto'
                            }}
                            onClick={() => handleSort(i)}
                        >
                            {header?.title}

                            {header?.title &&
                                <img
                                    alt='sort-icon'
                                    className="sort-icon"
                                    src={require('../../theme/assets/sort-icon.png')}
                                />
                            }
                        </li>
                    )
                })}
            </ul>

            {arr?.length ?
                <ul className='table-data-list-view'>
                    {arr.map(obj => {
                        const { className, id } = obj

                        return (
                            <li
                                className={`table-data-cell${onCellClick ? ' clickable' : ''}${className ? className : ''}`}
                                key={id}
                                onClick={() => {
                                    if (onCellClick) onCellClick(obj)
                                }}
                            >
                                {keys.map((key, i) => {
                                    return (
                                        <div
                                            className='cell-label ellipsis'
                                            style={{
                                                flex: headers[i]?.width ? '' : '1 1',
                                                width: headers[i]?.width ?? 'auto'
                                            }}
                                            key={key}
                                        >{obj[key]?.component ?? obj[key]}</div>
                                    )
                                })
                                }
                            </li>
                        )
                    })}
                </ul>
                :
                <div className='hint-label'>There are no items to display.</div>
            }
        </div>
    )
}

export default TableView