import PropTypes from 'prop-types'

const SelectMenu = (props) => {
    const { className, label, options, onChange, placeholder, style, value } = props
    const menuOptions = placeholder ? [{ id: 'hint', value: placeholder }, ...options] : options

    return (
        <div className={`select-menu-view${className ? ` ${className}` : ''}`}>
            <select
                className={`select-menu${label ? ' floating' : ''}`}
                onChange={e => onChange(e.target.value)}
                style={style}
                value={value ?? 'hint'}
            >
                {menuOptions.map(({ id, value }) =>
                    <option key={id} disabled={(id === 'hint')} value={id}>{value}</option>
                )}
            </select>

            {label && <div className='menu-label'>{label}</div>}

            <img
                alt='arrow-icon'
                className='arrow-icon'
                src={require('../../theme/assets/chevron-icon-grey.png')}
            />
        </div>
    )
}

SelectMenu.propTypes = {
    className: PropTypes.string,
    label: PropTypes.string,
    options: PropTypes.array,
    onChange: PropTypes.func,
    placeholder: PropTypes.string,
    style: PropTypes.object,
    value: PropTypes.string
}

export default SelectMenu