import PropTypes from 'prop-types'
import moment from 'moment'

import DateTextField from './DateTextField'

const DateRangePicker = ({ onChange, value }) => {
    const date = moment(value)

    function handleWeek(forward) {
        const d = moment(value)

        d.add((forward ? 7 : -7), 'day')

        onChange(d)
    }

    return (
        <div className='date-range-picker-view'>
            <div className='arrow-icon clickable' onClick={() => handleWeek(false)}>
                <img
                    alt='arrow-icon'
                    src={require('../../theme/assets/arrow-icon-grey.png')}
                />
            </div>

            <div className='date-frame'>
                <DateTextField
                    onChange={d => onChange(moment(d))}
                    value={value}
                />

                <div className='date-label'>{`${date.startOf('week').format('MMM D')} - ${date.endOf('week').format('D, YYYY')}`}</div>

                <img
                    alt='calendar-icon'
                    className='calendar-icon'
                    src={require('../../theme/assets/calendar-icon-orange.png')}
                />


            </div>

            <div className='arrow-icon forward clickable' onClick={() => handleWeek(true)}>
                <img
                    alt='arrow-icon'
                    src={require('../../theme/assets/arrow-icon-grey.png')}
                />
            </div>
        </div>
    )
}

DateRangePicker.propTypes = {
    onChange: PropTypes.func,
    value: PropTypes.string
}

export default DateRangePicker
