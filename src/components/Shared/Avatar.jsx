import PropTypes from "prop-types"

const Avatar = ({ user, width }) => {
    let preference = user?.preferences?.find(o => o.key === 'profile.color'),
        initials = `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`,
        attachment = user?.attachments?.find(a => a.label === 'profile_picture')

    if (attachment) {
        return (
            <img
                alt='avatar'
                className='avatar'
                src={attachment.contentUrl}
                style={{ width, height: width }}
            />
        )
    } else {
        return (
            <div
                className='avatar'
                style={{
                    background: preference?.value[0] || '#DAD8DA',
                    width,
                    height: width,
                    fontSize: width ? (width * .42) : 13
                }}
            >
                <div className='initials'>{initials}</div>
            </div>
        )
    }
}

Avatar.propTypes = {
    user: PropTypes.object.isRequired,
    width: PropTypes.number
}

export default Avatar