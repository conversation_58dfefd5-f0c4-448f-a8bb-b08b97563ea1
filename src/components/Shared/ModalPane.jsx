import { useEffect, useState } from "react"

const ANIMATION_TIMING = 150

export default function ModalPane(props) {
    const { className, content, hide } = props
    const [display, setDisplay] = useState(false)

    useEffect(() => {
        setTimeout(() => setDisplay(true), ANIMATION_TIMING)
    }, [])

    function animateOut() {
        setDisplay(false)
        setTimeout(hide, ANIMATION_TIMING)
    }

    return (
        <div className={`modal-pane${className ? ` ${className}` : ''}`}>
            <div
                className='background'
                onClick={animateOut}
            />

            <div className={`modal-pane-content ${display ? '' : 'slide-down'}`}>
                {content(animateOut)}
            </div>
        </div>
    )
}