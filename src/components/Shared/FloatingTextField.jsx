import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';

const FloatingTextField = (props) => {
    const { icon, iconSelected, label, maxLength, onChange, type, value } = props
    const [isFocused, setIsFocused] = useState(false);
    const [hasValue, setHasValue] = useState(Boolean(value));

    const handleFocus = () => {
        setIsFocused(true);
    };

    const handleBlur = (event) => {
        setIsFocused(false);
        setHasValue(!!event.target.value);
    };

    const handleChange = (event) => {
        setHasValue(!!event.target.value);
        onChange(event.target.value)
    };

    return (
        <div className={`floating-text-field ${isFocused || hasValue ? 'focused' : ''}`}>
            {(type === 'tel') ?
                <input
                    className='floating-input'
                    onBlur={handleBlur}
                    onChange={e => {
                        let phone = e.target.value.replace(/\D/g, ''); // Remove non-digit characters

                        if (phone.length > 3 && phone.length <= 6) {
                            phone = `(${phone.slice(0, 3)}) ${phone.slice(3)}`;
                        } else if (phone.length > 6) {
                            phone = `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6, 10)}`;
                        } else if (phone.length > 0) {
                            phone = `(${phone.slice(0, 3)}) ${phone.slice(3)}`;
                        }

                        handleChange({ target: { value: phone } })
                    }}
                    onFocus={handleFocus}
                    pattern="^\(\d{3}\) \d{3}-\d{4}$"  // (XXX) XXX-XXXX
                    type={'tel'}
                    value={value || ''}
                />
                :
                <input
                    className='floating-input'
                    maxLength={maxLength}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    onFocus={handleFocus}
                    type={type ?? 'text'}
                    value={value || ''}
                />
            }

            <label className='floating-label'>{label}</label>

            {(icon || iconSelected) &&
                <img
                    className='floating-icon'
                    alt='floating-icon'
                    src={(value && iconSelected) ? iconSelected : icon}
                />
            }
        </div>
    );
}

FloatingTextField.propTypes = {
    label: PropTypes.string,
    maxLength: PropTypes.number,
    onChange: PropTypes.func.isRequired,
    type: PropTypes.string,
    value: PropTypes.string
}

export default FloatingTextField