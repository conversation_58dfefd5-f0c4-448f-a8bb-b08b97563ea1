import { useContext, useState } from 'react'

import { orderBy } from 'lodash'
import <PERSON><PERSON> from 'js-cookie'

import { RoleContext } from '../../context/Role'


import Avatar from './Avatar'

const RoleSelector = ({ user }) => {
    const [modal, setModal] = useState(false)
    const { role, setRole } = useContext(RoleContext)
    const selectedRole = role

    function handleRole(role) {
        Cookie.set(`${process.env.REACT_APP_COOKIE_NAME}-role`, JSON.stringify(role))

        setRole(role)
        setModal(false)
    }

    if (!user) return

    return (
        <>
            <div className='role-selector-view clickable' onClick={() => setModal(true)}>
                <Avatar user={user} />

                <div className='middle-section'>
                    <div className='name bold'>{`${user?.firstName} ${user?.lastName}`}</div>
                    <div className={`role capitalize ${selectedRole ? '' : 'empty'}`}>{selectedRole ? `${selectedRole?.org?.name} - ${selectedRole.role}` : 'No Role Selected'}</div>
                </div>

                <img
                    alt='arrow-icon'
                    className='arrow-icon'
                    src={require('../../theme/assets/chevron-icon-grey.png')}
                />
            </div>

            {modal &&
                <div className='role-selection-menu'>
                    <div className='background full-screen-view' onClick={() => setModal(false)} />

                    <ul className='roles-list-view'>
                        {orderBy(user?.roles, ['org.name']).map(r => {
                            const { id, role: roleName, org } = r

                            return (
                                <li className='role-cell clickable' key={id} onClick={() => handleRole(r)}>
                                    <div className='cell-label capitalize'>{`${org.name} - ${roleName}`}</div>
                                    {(selectedRole.id === id) && <div className='cell-label selected'>(Current)</div>}
                                </li>
                            )
                        })}
                    </ul>
                </div>
            }
        </>
    )
}

export default RoleSelector