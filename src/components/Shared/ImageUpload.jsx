import { useState } from "react"
import Avatar from "./Avatar"

const ImageUpload = ({ onChange, user }) => {
    const [file, setFile] = useState()

    function handleFile(e) {
        const file = e.target.files[0]

        if (!file) {
            window.alert('No file selected')
            return
        }

        const reader = new FileReader()

        // Define what happens when the file is successfully read
        reader.onload = function (event) {
            const fileContent = event.target.result

            setFile(fileContent)

            if (onChange) onChange(fileContent)
        }

        // Define what happens if there's an error while reading the file
        reader.onerror = function () {
            window.alert('Error reading file')
        }

        reader.readAsDataURL(file)
    }

    return (
        <div className='image-upload-view'>
            {file || user?.id ?
                <>
                    {file ?
                        <img
                            alt='file-icon'
                            className='file-icon'
                            src={file}
                        />
                        :
                        <Avatar
                            width={192}
                            user={user}
                        />}


                    <div className='overlay-frame'>
                        {file && <button className='remove-button' onClick={() => setFile()}>Remove</button>}

                        <div className='choose-new-frame'>
                            <input
                                className='file-input full-screen-view clickable'
                                type='file'
                                onChange={handleFile}
                            />

                            <button className='choose-new-button'>Choose New</button>
                        </div>
                    </div>
                </>
                :
                <div className='add-photo-frame'>
                    <input
                        className='file-input full-screen-view clickable'
                        type='file'
                        onChange={handleFile}
                    />

                    <img
                        alt='camera-icon'
                        className='camera-icon'
                        src={require('../../theme/assets/camera-icon.png')}
                    />

                    <div className='add-photo-label'>Add Photo</div>
                </div>
            }
        </div>
    )
}

export default ImageUpload