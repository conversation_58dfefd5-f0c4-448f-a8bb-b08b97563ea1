import PropTypes from "prop-types"

const IconButton = ({ background, className, icon, iconAlign, iconHeight, onClick, title }) => {
    return (
        <div
            className={`icon-button-view clickable ${className ? className : ''}`}
            style={{ background: background ?? '#F7F8F8' }}
            onClick={onClick}
        >
            {(icon && iconAlign !== 'right') &&
                <img
                    alt='icon-button'
                    className='icon-button'
                    src={icon}
                    style={{ height: iconHeight ?? 18 }}
                />
            }

            <div className='button-title' style={{ color: title?.color ?? '#000' }}>{title.label}</div>

            {(icon && iconAlign === 'right') &&
                <img
                    alt='icon-button'
                    className='icon-button right'
                    src={icon}
                    style={{ height: iconHeight ?? 18 }}
                />
            }
        </div >
    )
}

IconButton.propTypes = {
    background: PropTypes.string,
    className: PropTypes.string,
    icon: PropTypes.string,
    onClick: PropTypes.func.isRequired,
    iconHeight: PropTypes.number,
    title: PropTypes.shape({
        color: PropTypes.string,
        label: PropTypes.string.isRequired
    }).isRequired
}

export default IconButton