import { useEffect, useState } from "react"
import ReactSlider from 'react-slider'

import moment from "moment"

import Checkbox from '../Shared/Checkbox'

const DaySchedules = (props) => {
    const { daySchedules = [], onChange } = props
    const days = moment.weekdays()
    const [schedules, setSchedules] = useState(daySchedules)

    useEffect(() => {
        if (onChange) onChange(schedules)
    }, [schedules])

    function addDaySchedule(day, dayIdx) {
        const arr = schedules.filter(o => o.day === dayIdx)
        const isEmpty = (arr.length === 0)
        const startTime = moment().set({
            h: (isEmpty ? 9 : 0),
            m: 0
        })

        setSchedules([...schedules, {
            day: dayIdx,
            dayStr: day.slice(0, 3),
            remove: false,
            create: true,
            concurrency: 1,
            startTime,
            startTimeEpoch: startTime.valueOf(),
            endTime: moment().set({ h: (isEmpty ? 17 : 1), m: 0 })
        }])
    }

    function handleDaySchedule(daySchedule, key, value, i) {
        let arr = schedules

        setSchedules([...arr.slice(0, i), { ...daySchedule, [key]: value }, ...arr.slice(i + 1, arr.length)])
    }

    function toggleDaySchedules(day) {
        const arr = []

        schedules.forEach(daySchedule => {
            const isDay = (daySchedule.day === day)

            if (isDay && daySchedule.create) {
                // do nothing - remove from array
            } else if (isDay) {
                arr.push({
                    ...daySchedule,
                    remove: isDay
                })
            } else {
                arr.push(daySchedule)
            }
        })

        setSchedules(arr)
    }

    function toggleDaySchedule(daySchedule, i, isRemove) {
        let arr = schedules

        if (!daySchedule) {
            // do nothing
        } else if (daySchedule.create) {
            // local - just remove it
            const idx = arr.findIndex(o => o === daySchedule)

            setSchedules([...arr.slice(0, idx), ...arr.slice(idx + 1, arr.length)])
        } else {
            daySchedule.remove = isRemove

            setSchedules([...arr.slice(0, i), daySchedule, ...arr.slice(i + 1, arr.length)])
        }
    }

    return (
        <ul className='day-schedules-list-view'>
            {days.map((day, i) => {
                let arr = schedules,
                    daySchedules = arr.filter(o => o.day === i),
                    showSchedule = ((daySchedules.length > 0) && (daySchedules.filter(o => !o.remove).length > 0)),
                    arrIdx = arr.findIndex(o => o.day === i),
                    sliderValues = daySchedules.map(d => {
                        const startMinutes = (d.startTime.hour() * 60 + d.startTime.minute())
                        const endMinutes = (d.endTime.hour() * 60 + d.endTime.minute())

                        return [startMinutes / 15, endMinutes / 15]
                    }).flat().sort()

                return (
                    <li className='day-schedule-cell' key={day}>
                        <div className='scheduler-header-frame'>
                            <Checkbox
                                title={day}
                                onChange={checked => {
                                    if (checked) {
                                        addDaySchedule(day, i)
                                    } else {
                                        toggleDaySchedules(i)
                                    }
                                }}
                                value={showSchedule}
                            />

                            {showSchedule &&
                                <button
                                    className='add-button'
                                    onClick={() => addDaySchedule(day, i)}
                                >+ Add Segment</button>}
                        </div>

                        {showSchedule &&
                            <div className='schedule-frame'>
                                <ReactSlider
                                    className='range-slider'
                                    trackClassName='track'
                                    thumbActiveClassName='thumb-active'
                                    onChange={values => {
                                        values.forEach((value, valueIdx) => {
                                            let sIdx = Math.floor(valueIdx / 2), // day schedule index
                                                even = (valueIdx % 2 === 0), // even is start time, odd is end time of the day schedule
                                                totalMin = value * 15,
                                                hours = Math.floor(totalMin / 60),
                                                min = ((totalMin / 60) % 1) * 60

                                            if (even) {
                                                daySchedules[sIdx].startTime = moment().set({ h: hours, m: min })
                                            } else {
                                                daySchedules[sIdx].endTime = moment().set({ h: hours, m: min })
                                            }
                                        })

                                        // set to existing day schedules + all the other ones
                                        setSchedules([...daySchedules, ...arr.filter(o => o.day !== i)])
                                    }}
                                    value={sliderValues.sort((a, b) => a - b)}
                                    renderTrack={(props, state) => {
                                        const isSegment = (state.index % 2 !== 0)

                                        return (
                                            <div
                                                {...props}
                                                key={state.index}
                                                className={`${props.className} ${isSegment ? 'segment' : ''}`}
                                            >
                                                {isSegment &&
                                                    <button
                                                        className='remove-button'
                                                        onClick={(e) => {
                                                            const trackIdx = (state.index - 2)
                                                            const idx = (trackIdx < 0) ? 0 : trackIdx
                                                            console.log(daySchedules)
                                                            toggleDaySchedule(daySchedules[idx], arrIdx, true)
                                                        }}
                                                    >Delete</button>
                                                }
                                            </div>
                                        )
                                    }}
                                    renderThumb={(props, state) => {
                                        const date = moment().startOf('day').add(state.valueNow * 15, 'minutes')
                                        const isEnd = (state.index % 2 !== 0)

                                        return (
                                            <div
                                                {...props}
                                                key={state.index}
                                                className={`thumb ${isEnd ? 'end' : ''}`}
                                            >
                                                <div className='date'>{date.format('h:mmA')}</div>
                                                <div className='dot'></div>
                                            </div>
                                        )
                                    }}
                                    pearling
                                    minDistance={4}
                                    min={0}
                                    max={96}
                                />

                                {/* 
                                <div className='segment-slots-frame'>
                                    <div className='section-header'>Slots Per Segment</div>

                                    <ul className='day-schedules-concurrency-list-view'>
                                        {daySchedules.map((daySchedule, i) => {
                                            const { concurrency, startTime, endTime } = daySchedule
                                            const dIdx = daySchedules.findIndex(o => o === daySchedule) // day schedules index

                                            return (
                                                <li
                                                    className='day-schedule-cell'
                                                    key={i}
                                                >
                                                    <div className='date'>{`${startTime.format('h:mmA')}-${endTime.format('h:mmA')}`}</div>

                                                    <SelectMenu
                                                        options={generateOptions(1, 50)}
                                                        onChange={e => handleDaySchedule(daySchedule, 'concurrency', parseInt(e.target.value), dIdx)}
                                                        value={concurrency || 1}
                                                    />

                                                    <button
                                                        className='remove-button'
                                                        onClick={() => toggleDaySchedule(daySchedule, arrIdx, true)}
                                                    >Remove</button>
                                                </li>
                                            )
                                        })}
                                    </ul>
                                </div> 
                                */}
                            </div>
                        }
                    </li>
                )
            })}
        </ul>
    )
}

export default DaySchedules