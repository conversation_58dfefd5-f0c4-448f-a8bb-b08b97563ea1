import PropTypes from 'prop-types'

const Checkbox = ({ onChange, readOnly, title, value }) => {
    return (
        <div
            className='checkbox-view'
            onClick={() => {
                if (!readOnly) onChange(!value)
            }}
        >
            <img
                alt='checkbox-icon'
                className='checkbox-icon'
                src={value ?
                    require('../../theme/assets/checkbox-2x-selected.png')
                    :
                    require('../../theme/assets/checkbox-2x-unselected.png')
                }
            />

            <div className={`checkbox-label ${value ? 'bold' : ''}`}>{title}</div>
        </div>
    )
}

Checkbox.propTypes = {
    onChange: PropTypes.func,
    readOnly: PropTypes.bool,
    title: PropTypes.string,
    value: PropTypes.bool
}

export default Checkbox