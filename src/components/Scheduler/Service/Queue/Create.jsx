import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router'

import moment from 'moment'

import { useMutation, useQuery } from '@apollo/client'

import ServiceQueueQuery from '../../../../graphql/queries/components/Scheduler/Service/Queue/Create'
import ServiceQueueAddMutation from '../../../../graphql/mutations/Service/Queue/Add'
import ServiceQueueCheckInMutation from '../../../../graphql/mutations/Service/Queue/CheckIn'

import { generateDaysOfWeek } from '../../../../helpers/Functions'
import useCurrentRole from '../../../../hooks/useCurrentRole'

import RosterUsersPane from '../../Roster/panes/Users'

import Avatar from '../../../Shared/Avatar'
import Breadcrumb from '../../../Shared/Breadcrumb'
// import Checkbox from '../../../Shared/Checkbox'
// import DateRangePicker from '../../../Shared/DateRangePicker'
// import HeaderValueLabel from '../../../Shared/HeaderValueLabel'
import IconButton from '../../../Shared/IconButton'
import LoadingPane from '../../../Shared/LoadingPane'
import SelectMenu from '../../../Shared/SelectMenu'
// import SegmentedButton from '../../../Shared/SegmentedButton'

const ServiceQueueCreate = () => {
    const { currentRole } = useCurrentRole()
    const navigate = useNavigate()

    const [dateButtons, setDateButtons] = useState([])

    const [onload, setOnload] = useState(true)
    const [modal, setModal] = useState(false)
    const [org, setOrg] = useState()
    const [member, setMember] = useState()
    const [reason, setReason] = useState('')
    const [service, setService] = useState()
    const [times, setTimes] = useState([])
    const [time, setTime] = useState()

    const [serviceQueueAdd, { loading: requestLoading }] = useMutation(ServiceQueueAddMutation)
    const [serviceQueueCheckIn, { loading: requestLoading2 }] = useMutation(ServiceQueueCheckInMutation)
    const { data, loading } = useQuery(ServiceQueueQuery, { variables: { id: currentRole.org.id } })

    const valid = (service && member?.id)

    useEffect(() => {
        if (onload && !loading) {
            handleDateSelected(moment())
            setOrg(data.org)
            setOnload(false)
        }
    }, [onload, loading, data])

    // fetch day appointment times
    useEffect(() => {
        if (valid) {
            const selected = dateButtons.find(o => o.selected)

            // providerTimes({
            //     variables: {
            //         org: currentRole.org.id,
            //         service: service.id,
            //         date: selected.date.format('MM/DD/YYYY')
            //     }
            // }).then(response => {
            //     setTimes(response.data.org.service.availableTimesIso ?? [])
            // }).catch(error => {
            //     window.alert(error.message)
            // })
        }
    }, [valid, dateButtons])

    function handleDateSelected(d) {
        const buttons = generateDaysOfWeek(d)
        const formattedDateButtons = buttons.map(b => {
            return {
                ...b,
                selected: b.date.startOf('day').isSame(d.startOf('day'))
            }
        })

        setDateButtons(formattedDateButtons)
    }

    // function handleSegmentedButton(button) {
    //     setDateButtons(dateButtons.map(o => ({ ...o, selected: o.date === button.date })))
    // }

    function save() {
        if (!valid) {
            window.alert('Select a service and member.')
            return
        }

        serviceQueueAdd({
            variables: {
                input: {
                    service,
                    person: member.id,
                    reason,
                    // scheduledAt: Math.round(moment(selectedService.nextAvailableAppt[0].startIso).valueOf() / 1000)
                }
            }
        }).then(response => {
            const { errors, queueEntry, success } = response.data.serviceQueueAdd

            if (success) {
                saveCheckIn(queueEntry)
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            window.alert(error.message)
        })
    }

    function saveCheckIn(queueEntry) {
        serviceQueueCheckIn({
            variables: {
                input: { id: queueEntry.id }
            }
        }).then(response => {
            const { errors, success } = response.data.serviceQueueCheckIn

            if (success) {
                navigate('/dashboard/appointments')
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            window.alert(error.message)
        })
    }

    if (onload || requestLoading || requestLoading2) return <LoadingPane />

    const selectedService = org?.services.find(s => s.id === service)

    return (
        <div className='service-queue-view full-screen-view'>
            <div className='toolbar'>
                <Breadcrumb
                    buttons={[{ title: 'Appointments', onClick: () => navigate('/dashboard/appointments') }, { title: 'Add Walk In' }]}
                />

                <div className='button-frame'>
                    <IconButton
                        onClick={() => navigate('/dashboard/appointments')}
                        title={{ label: 'Cancel' }}
                    />
                    <IconButton
                        className={valid ? '' : 'disabled'}
                        icon={require('../../../../theme/assets/check-icon-white.png')}
                        iconHeight={9.17}
                        background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                        onClick={save}
                        title={{
                            color: 'white',
                            label: 'Save'
                        }}
                    />
                </div>
            </div>

            <div className='content-frame scroll'>
                <div className='section-frame'>
                    <div className='section-header'>Appointment Details</div>

                    <div className='form-frame'>
                        <SelectMenu
                            options={org.services.filter(s => s.kind === 'queue' && s.status === 'active').map(s => ({ id: s.id, value: s.title }))}
                            onChange={value => setService(value)}
                            placeholder='Select appointment type'
                            value={service}
                        />

                        {member ?
                            <div className='member-frame'>
                                <div className='header'>Member</div>

                                <div className='member-details-frame'>
                                    <Avatar user={member} width={22} />

                                    <div className='name'>{`${member.firstName} ${member.lastName} - `}</div>

                                    <button className='select-member-button' onClick={() => setModal(true)}>Select Member</button>
                                </div>
                            </div>
                            :
                            <div className='member-hint-frame clickable' onClick={() => setModal(true)}>
                                <div className='hint-label'>Select Member</div>

                                <img
                                    alt='search-icon'
                                    className='search-icon'
                                    src={require('../../../../theme/assets/search-icon-orange.png')}
                                />
                            </div>
                        }

                        <div className='member-frame' />

                        {/* 
                        {valid && selectedService.nextAvailableAppt?.length ?
                            <HeaderValueLabel
                                header='Date & Time'
                                value={moment(selectedService.nextAvailableAppt[0].startIso).format('ddd - MMM D, YYYY h:mm A')}
                            />
                            :
                            <div className='member-frame'></div>
                        } 
                        */}

                        <textarea
                            className='form-textarea'
                            placeholder='Enter additional information...'
                            onChange={e => setReason(e.target.value)}
                            value={reason}
                        />
                    </div>
                </div>

                {/* <div className='section-frame'>
                    <div className='section-header'>Dates and Time</div>

                    {valid ?
                        <div className='available-times-frame'>
                            <div className='top-section'>
                                <DateRangePicker
                                    onChange={d => handleDateSelected(d)}
                                    value={dateButtons.find(o => o.selected)?.date.toISOString()}
                                />

                                <SegmentedButton
                                    buttons={dateButtons}
                                    onClick={button => handleSegmentedButton(button)}
                                />
                            </div>

                            {times?.length ?
                                <ul className='times-list-view'>
                                    {times.map(t => {
                                        const startTime = moment(t).format('h:mmA')
                                        const endTime = moment(t).add(service.durationMins, 'minutes').format('h:mmA')
                                        const selected = (time === t)

                                        return (
                                            <li
                                                key={t}
                                                className='time-cell'
                                            >
                                                <Checkbox
                                                    // readOnly={true}
                                                    onChange={() => setTime(t)}
                                                    title={`${startTime}-${endTime}`}
                                                    value={selected}
                                                />
                                            </li>
                                        )
                                    })}
                                </ul>
                                :
                                <div className='hint-label'>There are no appointment times to display for this day.</div>
                            }
                        </div>
                        :
                        <div className='hint-label'>Add information above to see available appointment slots</div>
                    }
                </div> */}
            </div>

            {modal &&
                <RosterUsersPane
                    onChange={user => setMember(user)}
                    onHide={() => setModal(false)}
                    users={org.users.nodes}
                />
            }
        </div>
    )
}

export default ServiceQueueCreate