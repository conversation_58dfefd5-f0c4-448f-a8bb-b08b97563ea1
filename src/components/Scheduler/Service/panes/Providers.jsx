import { useEffect, useState } from 'react'

import _ from 'lodash'

import Avatar from '../../../Shared/Avatar'
import Checkbox from '../../../Shared/Checkbox'
import IconButton from '../../../Shared/IconButton'
import ModalPane from '../../../Shared/ModalPane'
import TableView from '../../../Shared/TableView'

const ServiceProvidersPane = ({ handleChange, onHide, org, service }) => {
    const [arr, setArr] = useState([])
    const [selected, setSelected] = useState([])

    useEffect(() => {
        const arr = service.providers

        if (arr?.length) setSelected(arr)
    }, [])

    useEffect(() => {
        setArr(org.users.nodes.map(u => {
            const email = u.emails.find(o => o.label === 'main')
            const phone = u.phones.find(o => o.label === 'main')
            const gender = u.latestData.find(o => o.key === 'person.gender')

            return {
                ...u,
                fullName: `${u.firstName} ${u.lastName}`,
                email: email?.address,
                phone: phone?.number,
                gender: _.startCase(gender.values[0])
            }
        }))
    }, [org.users.nodes])

    function handleSelection(u) {
        const arr = selected
        const i = arr.findIndex(o => o === u.id)

        if (i >= 0) {
            setSelected([...arr.slice(0, i), ...arr.slice(i + 1, arr.length)])
        } else {
            setSelected([...arr, u.id])
        }
    }

    function save(hide) {
        hide()

        setTimeout(() => handleChange('providers', selected), 300)
    }

    return (
        <ModalPane
            className='services-providers-pane'
            content={hide =>
                <>
                    <div className='modal-header-frame' >
                        <div className='modal-header'>Select Provider(s)</div>

                        <img
                            alt='x-icon'
                            className='x-icon'
                            src={require('../../../../theme/assets/x-icon.png')}
                            onClick={hide}
                        />
                    </div>

                    <div className='modal-content-frame'>
                        <TableView
                            headers={[
                                { title: 'Name', width: '30%' },
                                { title: 'Gender', width: '20%' },
                                { title: 'Email', width: '30%' },
                                { title: 'Phone', width: '20%' },
                            ]}
                            data={arr.map(user => {
                                const checked = selected.includes(user.id)

                                return {
                                    ...user,
                                    nameFrame: (
                                        <div className='name-frame'>
                                            <Checkbox
                                                readOnly={true}
                                                value={checked}
                                            />

                                            <Avatar
                                                width={32}
                                                user={user}
                                            />

                                            <div className='cell-label full-name ellipsis'>{user.fullName}</div>
                                        </div>
                                    )
                                }
                            })}
                            keys={['nameFrame', 'gender', 'email', 'phone']}
                            onCellClick={(user) => handleSelection(user)}
                        />
                    </div>

                    <div className='modal-bottom-toolbar-frame'>
                        <IconButton
                            icon={require('../../../../theme/assets/check-icon-white.png')}
                            iconHeight={9.17}
                            background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                            onClick={() => save(hide)}
                            title={{
                                color: 'white',
                                label: 'Save'
                            }}
                        />
                    </div>
                </>
            }
            hide={onHide}
        />
    )
}

export default ServiceProvidersPane