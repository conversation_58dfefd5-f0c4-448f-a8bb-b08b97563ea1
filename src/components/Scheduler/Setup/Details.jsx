import { useEffect, useState } from 'react'
import { useMutation } from '@apollo/client'

import OrgUpdateMutation from '../../../graphql/mutations/Org/Update'

import useConstants from '../../../hooks/useConstants'

import FloatingTextField from '../../Shared/FloatingTextField'

import HeaderValueLabel from '../../Shared/HeaderValueLabel'
import IconButton from '../../Shared/IconButton'
import LoadingPane from '../../Shared/LoadingPane'
import SelectMenu from '../../Shared/SelectMenu'
import SetupInsurancesPane from './panes/Insurances'
import TableView from '../../Shared/TableView'

const SetupDetails = (props) => {
    const { edit, org, setEdit, setOrg } = props
    const { insurances, orgKinds, usaStates } = useConstants()

    // internal component state for editing
    const [organization, setOrganization] = useState()
    const [modal, setModal] = useState(false)

    const address = org.addresses.find(o => o.label === 'main')
    const phone = org.phones.find(o => o.label === 'main')
    const supportedIns = org?.preferences?.find(o => o.key === 'insurances')

    const [orgUpdate, { loading }] = useMutation(OrgUpdateMutation)

    useEffect(() => {
        parseOrg(org)
    }, [org])

    function parseOrg(o) {
        setOrganization({
            ...o,
            ...address,
            id: o.id,
            phone: phone?.number,
            insurances: supportedIns?.value ?? []
        })
    }

    function handleChange(key, value) {
        setOrganization({
            ...organization,
            [key]: value
        })
    }

    function handleEdit() {
        setEdit(true)
        parseOrg(org)
    }

    function save() {
        const { kind, id, insurances, name, phone, street, street2, city, state, zip } = organization
        const data = {
            id,
            kind,
            name
        }

        if (phone) data.phones = [{ number: phone }]
        if (street && city && state && zip) data.addresses = [{ street, street2, city, state, zip }]
        if (insurances?.length) data.preferences = [{ key: 'insurances', value: insurances.map(i => i.toString()) }]

        orgUpdate({
            variables: {
                input: data
            }
        }).then(response => {
            const { errors, organization: result } = response.data.orgUpdate

            if (result) {
                setOrg(result)
                setEdit(false)
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            window.alert(error.message)
        })
    }

    if (loading || !organization) return <LoadingPane />

    return (
        <div className='scheduler-setup-details-view'>
            {edit &&
                <div className='floating-button-frame'>
                    <IconButton
                        onClick={() => setEdit(false)}
                        title={{
                            color: '#747A7A',
                            label: 'Cancel'
                        }}
                    />

                    <IconButton
                        icon={require('../../../theme/assets/check-icon-white.png')}
                        iconHeight={9.17}
                        background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                        onClick={save}
                        title={{
                            color: 'white',
                            label: 'Save'
                        }}
                    />
                </div>
            }

            <div className='section-frame'>
                <div className='section-header-frame'>
                    <div className='section-header'>Network Details</div>

                    {!edit &&
                        <IconButton
                            icon={require('../../../theme/assets/edit-icon-orange.png')}
                            onClick={handleEdit}
                            title={{
                                color: '#E97100',
                                label: 'Update Details'
                            }}
                        />
                    }
                </div>

                {edit ?
                    <div className='form-frame'>
                        <FloatingTextField
                            label='Network title'
                            onChange={text => handleChange('name', text)}
                            value={organization?.name}
                        />

                        <SelectMenu
                            label='Network type'
                            options={orgKinds}
                            onChange={value => handleChange('kind', value)}
                            placeholder='Select network type'
                            value={organization.kind}
                        />

                        <div className='floating-text-field' />

                        <FloatingTextField
                            label='Street 1'
                            onChange={text => handleChange('street', text)}
                            value={organization.street}
                        />

                        <FloatingTextField
                            label='Street 2'
                            onChange={text => handleChange('street2', text)}
                            value={organization.street2}
                        />

                        <FloatingTextField
                            label='City'
                            onChange={text => handleChange('city', text)}
                            value={organization.city}
                        />

                        <SelectMenu
                            label='State'
                            options={usaStates}
                            onChange={value => handleChange('state', value)}
                            placeholder='Select state'
                            value={organization.state}
                        />

                        <FloatingTextField
                            label='Zip code'
                            maxLength={6}
                            onChange={text => handleChange('zip', text)}
                            value={organization.zip}
                        />

                        <FloatingTextField
                            label='Phone'
                            onChange={text => handleChange('phone', text)}
                            type='tel'
                            value={organization.phone}
                        />
                    </div>
                    :
                    <div className='form-frame'>
                        <HeaderValueLabel
                            header='Title of Network'
                            value={org.name}
                        />

                        <HeaderValueLabel
                            header='Network type'
                            value={orgKinds.find(o => o.id === org.kind).value}
                        />

                        <HeaderValueLabel
                            header='Address'
                            value={address?.address}
                        />

                        <HeaderValueLabel
                            header='Contact Phone'
                            value={phone?.number}
                        />
                    </div>
                }
            </div>

            <div className='section-frame'>
                <div className='section-header-frame'>
                    <div className='section-header'>Accepted Insurances</div>

                    {edit &&
                        <button className='add-button' onClick={() => setModal(true)}>+ Add Insurance</button>
                    }
                </div>

                {edit ?
                    <TableView
                        headers={[
                            { title: 'Carrier', width: '33%' },
                            { title: 'Plan Type', width: '33%' },
                        ]}
                        data={organization.insurances?.map(i => insurances.find(o => o.id === parseInt(i))) ?? []}
                        keys={['carrier', 'type']}
                    />
                    :
                    <div>
                        {supportedIns ?
                            <ul className='insurances-list-view'>
                                {supportedIns?.value?.map(ins => {
                                    const insurance = insurances.find(o => o.id === parseInt(ins))

                                    return (
                                        <li className='insurance-cell' key={insurance.id}>
                                            <div className='cell-label carrier'>{insurance?.carrier}</div>
                                            <div className='cell-label type'>{insurance?.type}</div>
                                        </li>
                                    )
                                })}
                            </ul>
                            :
                            <div className='hint-label'>There are no insurances to display.</div>
                        }
                    </div>
                }
            </div>

            {modal &&
                <SetupInsurancesPane
                    handleChange={handleChange}
                    onHide={() => setModal(false)}
                    insurances={organization.insurances}
                />
            }
        </div>
    )
}

export default SetupDetails