import { useContext, useEffect, useState } from 'react'
import { useNavigate, useLocation, useParams } from 'react-router'

import { useQuery } from '@apollo/client'

import SchedulerSetupQuery from '../../../graphql/queries/components/Scheduler/Setup'

import { RoleContext } from '../../../context/Role'

import Details from './Details'
import Roster from '../Roster'
import RosterUser from '../Roster/User'
import Services from '../Services'
import Service from '../Service'

import Breadcrumb from '../../Shared/Breadcrumb'
import LoadingPane from '../../Shared/LoadingPane'
import SegmentedButton from '../../Shared/SegmentedButton'

const SchedulerSetup = () => {
    const { role } = useContext(RoleContext)

    const [onload, setOnload] = useState(true)
    const [org, setOrg] = useState()
    const [edit, setEdit] = useState(false)
    const [buttons, setButtons] = useState([
        { title: 'Network Details', route: 'details' },
        { title: 'Staff Roster', route: 'roster' },
        { title: 'Services', route: 'services' }
    ])

    const navigate = useNavigate()
    const location = useLocation()
    const params = useParams()

    const { data, loading } = useQuery(SchedulerSetupQuery, { variables: { id: role.org.id } })

    useEffect(() => {
        if (onload && !loading) {
            setOrg(data.org)
            setOnload(false)
        }
    }, [onload, loading, data])

    // will handle segmented button controls
    useEffect(() => {
        if (!params.tab) navigate('/dashboard/setup/details')
        else {
            setButtons(buttons.map(b => ({ ...b, selected: params.tab === b.route })))
        }
    }, [location, role])

    function generateBreadcrumb() {
        const tab = buttons.find(b => b.selected)
        const route = tab?.route
        const arr = [{ title: 'Setup' }]


        if (route === 'details' && edit) arr.push({ title: 'Update Network Details' })
        if (route === 'roster' && params.id) arr.push({ title: 'Staff', onClick: () => navigate('/dashboard/setup/roster') }, { title: `${params.id === 'add' ? 'Add' : 'Edit'} Staff Member` })

        return arr
    }

    function handleSegmentedButton(button) {
        navigate(`/dashboard/setup/${button.route}`)
    }

    function renderContentView() {
        const route = params.tab

        if (route === 'roster') {
            return params.id ?
                <RosterUser
                    edit={edit}
                    org={org}
                    navigate={navigate}
                    params={params}
                    setEdit={setEdit}
                />
                :
                <Roster
                    navigate={navigate}
                    setEdit={setEdit}
                />
        } else if (route === 'services') {
            return params.id ?
                <Service
                    edit={edit}
                    org={org}
                    navigate={navigate}
                    params={params}
                    setEdit={setEdit}
                />
                :
                <Services
                    org={org}
                    navigate={navigate}
                    setEdit={setEdit}
                />
        } else {
            return (
                <Details
                    edit={edit}
                    org={org}
                    setEdit={setEdit}
                    setOrg={setOrg}
                />
            )
        }
    }

    if (onload || loading) return <LoadingPane />

    return (
        <div className='scheduler-setup-view full-screen-view'>
            <div className='toolbar'>
                <Breadcrumb
                    buttons={generateBreadcrumb()}
                />

                {!edit &&
                    <SegmentedButton
                        buttons={buttons}
                        onClick={handleSegmentedButton}
                    />
                }
            </div>

            <div className='content-frame'>
                {renderContentView()}
            </div>
        </div>
    )
}

export default SchedulerSetup