import { useState } from 'react'
import useConstants from '../../../../hooks/useConstants'

import IconButton from '../../../Shared/IconButton'
import ModalPane from '../../../Shared/ModalPane'
import SelectMenu from '../../../Shared/SelectMenu'

const SchedulerAppointmentStatusPane = ({ appt, onHide, save }) => {
    const { availableActions } = appt
    const { apptStatuses } = useConstants()

    const [status, setStatus] = useState(appt?.status)
    const [desc, setDesc] = useState('')

    const options = availableActions.filter(action => apptStatuses[action.split('!')[0]])
    const formattedStatus = apptStatuses[status]

    return (
        <ModalPane
            className='appointment-status-pane'
            content={hide =>
                <>
                    <div className='modal-header-frame' >
                        <div className='modal-header'>Update Status</div>

                        <img
                            alt='x-icon'
                            className='x-icon'
                            src={require('../../../../theme/assets/x-icon.png')}
                            onClick={hide}
                        />
                    </div>

                    <div className='modal-content-frame'>
                        <SelectMenu
                            label='Current Status'
                            options={options.map(action => ({
                                id: action,
                                value: apptStatuses[action.split('!')[0]].title
                            }))}
                            onChange={value => setStatus(value.split('!')[0])}
                            placeholder={formattedStatus.title}
                            style={{ color: formattedStatus.color, fontWeight: 500 }}
                        />

                        <textarea
                            className='modal-textarea'
                            placeholder='Enter optional description...'
                            onChange={e => setDesc(e.target.value)}
                            value={desc}
                        />
                    </div>

                    <div className='modal-bottom-toolbar-frame'>
                        <IconButton
                            icon={require('../../../../theme/assets/check-icon-white.png')}
                            iconHeight={9.17}
                            background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                            onClick={() => save(status, desc, hide)}
                            title={{
                                color: 'white',
                                label: 'Save'
                            }}
                        />
                    </div>
                </>
            }
            hide={onHide}
        />
    )
}

export default SchedulerAppointmentStatusPane