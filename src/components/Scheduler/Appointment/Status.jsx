import moment from 'moment'

import useConstants from '../../../hooks/useConstants'

const AppointmentStatus = (props) => {
    const { lastChangeAtIso, status } = props.appointment
    const { apptStatuses } = useConstants()

    const apptStatus = apptStatuses[status]

    function generateDurationMins(lastChangeAtIso) {
        const diffMins = moment().diff(moment(lastChangeAtIso), 'minutes')

        if (status === 'completed') {
            return moment(lastChangeAtIso).format('h:mm A')
        } if (diffMins > 60) {
            // Calculate hours and minutes from the total difference
            const hours = Math.floor(diffMins / 60);
            const minutes = diffMins % 60;

            return `${hours} hrs ${minutes} mins`
        } else {
            return `${diffMins} mins`
        }
    }

    return (
        <div
            className='appointment-status-view'
            style={{ display: 'flex', alignItems: 'center' }}
        >
            <div style={{ color: apptStatus.color }}>{apptStatus.title}</div>

            {apptStatus.showDuration &&
                <div style={{ marginLeft: 5 }}>{` - ${generateDurationMins(lastChangeAtIso)}`}</div>
            }
        </div>
    )
}

export default AppointmentStatus