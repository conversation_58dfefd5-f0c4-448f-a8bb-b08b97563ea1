import { useEffect, useState } from 'react'

import _ from 'lodash'

import { useQuery } from '@apollo/client'

import useConstants from '../../../hooks/useConstants'

import SchedulerServicesQuery from '../../../graphql/queries/components/Scheduler/Services'

import LoadingPane from '../../Shared/LoadingPane'
import IconButton from '../../Shared/IconButton'
import TableView from '../../Shared/TableView'

const SetupServices = (props) => {
    const { navigate, org } = props
    const { serviceKinds } = useConstants()

    const [onload, setOnload] = useState(true)
    const [services, setServices] = useState([])

    const { data, loading } = useQuery(SchedulerServicesQuery, { variables: { id: org.id } })

    useEffect(() => {
        if (onload && !loading) {
            setServices(data.org.services.map(service => ({
                ...service,
                days: service?.daySchedules ?
                    _.uniqBy(service?.daySchedules, 'dayName').map(o => o.dayName).toString().replace(/,/g, ', ')
                    :
                    '-',
                type: serviceKinds.find(o => o.id === service.kind).value
            })))
            setOnload(false)
        }
    }, [onload, loading, data])

    function goToServiceRoute(id) {
        navigate(`/dashboard/setup/services/${id}`)
    }

    if (onload) return <LoadingPane />

    const arr = services

    return (
        <div className='setup-services-view'>
            <div className='toolbar' style={{ padding: 0 }}>
                <div className='toolbar-header'>{`Services (${services.length})`}</div>

                <div className='right-section'>
                    <IconButton
                        icon={require('../../../theme/assets/plus-icon-white.png')}
                        iconHeight={12}
                        background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                        onClick={() => goToServiceRoute('add')}
                        title={{
                            color: 'white',
                            label: 'Add Service'
                        }}
                    />
                </div>
            </div>

            {arr?.length ?
                <TableView
                    headers={[
                        { title: 'Title', width: '33%' },
                        { title: 'Type', width: '33%' },
                        { title: 'Days', width: '33%' },
                        { title: 'Providers', width: '33%' },
                    ]}
                    data={arr}
                    keys={['title', 'type', 'days', 'providerCount']}
                    onCellClick={(service) => goToServiceRoute(service.id)}
                />
                :
                <div className='hint-frame'>
                    No services have been added.
                    <button className='hint-button' onClick={() => goToServiceRoute('add')}>+ Add Service</button>
                </div>
            }
        </div>
    )
}

export default SetupServices