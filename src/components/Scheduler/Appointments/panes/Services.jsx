import { useEffect, useState } from 'react'
import { useMutation } from '@apollo/client'

import _ from 'lodash'

import ServiceActivateMutation from '../../../../graphql/mutations/Service/Activate'
import ServiceDeactivateMutation from '../../../../graphql/mutations/Service/Deactivate'

import useConstants from '../../../../hooks/useConstants'

import Checkbox from '../../../Shared/Checkbox'

const SchedulerAppointmentsServicesPane = (props) => {
    const { org } = props
    const { apptKinds } = useConstants()
    const [expand, setExpand] = useState(false)
    const [arr, setArr] = useState([])

    const [serviceActivate] = useMutation(ServiceActivateMutation)
    const [serviceDeactivate] = useMutation(ServiceDeactivateMutation)

    useEffect(() => {
        if (org?.services) setArr(org.services)
    }, [org])

    function handleService(service) {
        const mutation = (service.status === 'active') ? serviceDeactivate : serviceActivate
        const i = arr.findIndex(s => s.id === service.id)

        mutation({
            variables: {
                input: { id: service.id }
            }
        }).then(response => {
            const key = Object.keys(response.data)[0]
            const { errors, service } = response.data[key]

            if (service) {
                setArr([...arr.slice(0, i), service, ...arr.slice(i + 1, arr.length)])
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            window.alert(error.message)
        })
    }

    return (
        <div className={`services-pane${expand ? ' expand' : ''}`}>
            <div
                className='header-frame clickable'
                onClick={() => setExpand(true)}
            >
                <div className='services-label'>{`Services Currently Online (${arr.filter(s => s.status === 'active').length}/${arr.length})`}</div>

                <img
                    alt='arrow-icon'
                    className='arrow-icon'
                    src={require('../../../../theme/assets/arrow-icon-grey.png')}
                    onClick={() => setExpand(true)}
                />
            </div>


            <div className='floating-frame clickable' style={{ height: expand ? (44 + (60 * arr.length)) : 0 }}>
                <div
                    className='header-frame'
                    onClick={() => setExpand(false)}
                >
                    <div className='services-label'>{`Services Currently Online (${arr.filter(s => s.status === 'active').length}/${arr.length})`}</div>

                    <img
                        alt='arrow-icon'
                        className='arrow-icon'
                        src={require('../../../../theme/assets/arrow-icon-grey.png')}
                    />
                </div>

                <ul className='services-list-view'>
                    {_.orderBy(arr, 'title').map((service, i) => {
                        const { durationMins, kind, id, status, title } = service

                        return (
                            <li
                                className='service-cell'
                                key={id}
                                onClick={() => handleService(service)}
                            >
                                <Checkbox
                                    readOnly={true}
                                    value={status === 'active'}
                                />

                                <div className='flex-frame'>
                                    <div className='cell-label bold'>{title}</div>
                                    <div className='cell-label'>{`${durationMins}min - ${apptKinds[kind]?.title}`}</div>
                                </div>
                            </li>
                        )
                    })}
                </ul>
            </div>
        </div>
    )
}

export default SchedulerAppointmentsServicesPane