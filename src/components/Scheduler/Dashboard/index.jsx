import { useContext, useEffect, useState } from "react"
import { useNavigate, useParams, useLocation, Outlet, Routes, Route } from "react-router"

import { UserContext } from '../../../context/User'
import { RoleContext } from '../../../context/Role'

import DashboardNavigation from "./Navigation"

import Account from "../../Account"
import Appointments from "../Appointments"
import Setup from "../Setup"

import RoleSelector from "../../Shared/RoleSelector"

const Dashboard = (props) => {
    const user = useContext(UserContext)
    const { role } = useContext(RoleContext)

    const [selectedRole, setSelectedRole] = useState(role)

    const navigate = useNavigate()
    const params = useParams()
    const location = useLocation()

    useEffect(() => {
        if (location.pathname === '/dashboard') navigate('/dashboard/appointments')
    }, [])

    useEffect(() => {
        // handles any role changes or changes from another tab
        if (role.id !== selectedRole.id) window.location.reload()
    }, [role])

    function renderContentView() {
        const route = params.type
        const Todo = (
            <div style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                textTransform: 'capitalize',
                color: 'grey',
                fontStyle: 'italic'
            }}>{`${route} coming soon...`}</div>
        )
        const Components = {
            account: Account,
            setup: Setup,
            appointments: Appointments,
            reporting: Todo,
        }

        if (Components[route]) {
            const Component = Components[route]

            return (typeof Component === 'object') ? Todo : <Component />
        } else {
            return Todo
        }
    }

    return (
        <div className='dashboard-view full-screen-view'>
            <div className='toolbar'>
                <div className='platform-button clickable' onClick={() => navigate('/welcome')}>
                    <img
                        alt='platform-icon'
                        className='platform-icon'
                        src={require('../../../theme/assets/platform-icon.png')}
                    />

                    <div className='platform-label'>Scheduler</div>
                </div>

                <div className='right-section'>
                    <RoleSelector user={user} />

                    <div className='notifications-frame'>
                        <img
                            className='bell-icon'
                            alt='bell-icon'
                            src={require('../../../theme/assets/bell-icon.png')}
                        />
                    </div>
                </div>
            </div>

            <div className='content-frame'>
                <DashboardNavigation
                    navigate={navigate}
                    location={location}
                    params={params}
                />

                <div className='document-frame scroll'>
                    <Outlet />
                </div>
            </div>
        </div>
    )
}

export default Dashboard