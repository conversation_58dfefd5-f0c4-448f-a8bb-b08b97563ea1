import { useParams, useNavigate } from "react-router"

const buttons = [
    { name: 'Setup', route: 'setup' },
    { name: 'Appointments', route: 'appointments' },
    { name: 'Reporting', route: 'reporting' },
    { name: 'Account', route: 'account' }
]

const DashboardNavigation = ({ navigate, location, params }) => {
    return (
        <div className='dashboard-navigation-view'>
            <ul className='navigation-list-view'>
                {buttons.map(button => {
                    const selected = (location.pathname.split('/')[2] === button.route)

                    return (
                        <li
                            key={button.route}
                            className={`navigation-cell clickable ${selected ? 'selected' : ''}`}
                            onClick={() => navigate(`/dashboard/${button.route}`)}
                        >
                            <img
                                alt='nav-icon'
                                className='nav-icon'
                                src={require(`../../../theme/assets/navigation/${button.route}-${selected ? 'icon-selected' : 'icon'}.png`)}
                            />

                            <div className='cell-label'>{button.name}</div>
                        </li>
                    )
                })}
            </ul>
        </div>
    )
}

export default DashboardNavigation