import { useEffect, useState } from 'react'

import _ from 'lodash'
import moment from 'moment'

import Avatar from '../../../Shared/Avatar'
import Checkbox from '../../../Shared/Checkbox'
import IconButton from '../../../Shared/IconButton'
import ModalPane from '../../../Shared/ModalPane'
import TableView from '../../../Shared/TableView'

const RosterUsersPane = ({ onChange, onHide, users }) => {
    const [arr, setArr] = useState([])
    const [selected, setSelected] = useState()

    useEffect(() => {
        setArr(users.map(u => {
            const email = u.emails.find(o => o.label === 'main')
            const phone = u.phones.find(o => o.label === 'main')
            const gender = u.latestData.find(o => o.key === 'person.gender')

            return {
                ...u,
                fullName: `${u.firstName} ${u.lastName}`,
                email: email?.address,
                phone: phone?.number,
                gender: _.startCase(gender.values[0])
            }
        }))
    }, [users])

    function handleSelection(u) {
        setSelected(u)
    }

    function save(hide) {
        hide()

        setTimeout(onChange(selected), 300)
    }

    return (
        <ModalPane
            className='services-users-pane'
            content={hide =>
                <>
                    <div className='modal-header-frame' >
                        <div className='modal-header'>Select Member</div>

                        <img
                            alt='x-icon'
                            className='x-icon'
                            src={require('../../../../theme/assets/x-icon.png')}
                            onClick={hide}
                        />
                    </div>

                    <div className='modal-content-frame'>
                        <TableView
                            headers={[
                                { title: 'Name', width: '25%' },
                                { title: 'Date of Birth', width: '20%' },
                                { title: 'Contact', width: '25%' },
                                { title: 'Insurance', width: '15%' },
                                { title: 'Household / Team', width: '15%' },
                            ]}
                            data={arr.map(user => {
                                const checked = (user.id === selected?.id)
                                const email = user?.emails.find(o => o.label === 'main')
                                const phone = user?.phones.find(o => o.label === 'main')

                                return {
                                    ...user,
                                    nameFrame: (
                                        <div className='name-frame'>
                                            <Checkbox
                                                readOnly={true}
                                                value={checked}
                                            />

                                            <Avatar
                                                width={32}
                                                user={user}
                                            />

                                            <div className='cell-label full-name ellipsis'>{user.fullName}</div>
                                        </div>
                                    ),
                                    dobFrame: (
                                        <div className='stacked-frame'>
                                            <div className='cell-label'>{moment(user.dob).format('M/D/YYYY')}</div>
                                            <div className='cell-label'>{`${moment().diff(moment(user.dob), 'years')} yrs`}</div>
                                        </div>
                                    ),
                                    contactFrame: (
                                        <div className='stacked-frame'>
                                            <div className='cell-label'>{phone?.number ?? '-'}</div>
                                            <div className='cell-label'>{email?.address ?? '-'}</div>
                                        </div>
                                    ),
                                    insuranceFrame: (
                                        <div className='stacked-frame'>
                                            <div className='cell-label'>-</div>
                                            <div className='cell-label'>-</div>
                                        </div>
                                    ),
                                    householdFrame: (
                                        <div className='stacked-frame'>
                                            <div className='cell-label'>-</div>
                                            <div className='cell-label'>-</div>
                                        </div>
                                    )
                                }
                            })}
                            keys={['nameFrame', 'dobFrame', 'contactFrame', 'insuranceFrame', 'householdFrame']}
                            onCellClick={(user) => handleSelection(user)}
                        />
                    </div>

                    <div className='modal-bottom-toolbar-frame'>
                        <IconButton
                            icon={require('../../../../theme/assets/check-icon-white.png')}
                            iconHeight={9.17}
                            background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                            onClick={() => save(hide)}
                            title={{
                                color: 'white',
                                label: 'Save'
                            }}
                        />
                    </div>
                </>
            }
            hide={onHide}
        />
    )
}

export default RosterUsersPane