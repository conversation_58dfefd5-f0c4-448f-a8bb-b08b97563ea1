import { useEffect, useState } from "react"
import { useLazyQuery, useMutation } from "@apollo/client"

import moment from "moment"

import SchedulerRosterUserQuery from "../../../graphql/queries/components/Scheduler/Roster/User"
import UserFindOrCreateMutation from "../../../graphql/mutations/User/FindOrCreate"
import UserUpdateMutation from "../../../graphql/mutations/User/Update"

import useConstants from "../../../hooks/useConstants"

import DaySchedules from "../../Shared/DaySchedules"
import DateTextField from "../../Shared/DateTextField"
import FloatingTextField from "../../Shared/FloatingTextField"
import IconButton from "../../Shared/IconButton"
import ImageUpload from "../../Shared/ImageUpload"
import LoadingPane from "../../Shared/LoadingPane"
import SelectMenu from "../../Shared/SelectMenu"

const SetupRosterUser = (props) => {
    const { edit, navigate, org, params, setEdit } = props

    // hooks
    const constants = useConstants()

    const [requesting, setRequesting] = useState(false)
    const [user, setUser] = useState()
    const [step, setStep] = useState(0)
    const [steps] = useState([Step1, Step2])

    const [getUser, { loading }] = useLazyQuery(SchedulerRosterUserQuery)
    const [userFindOrCreate] = useMutation(UserFindOrCreateMutation)
    const [userUpdate] = useMutation(UserUpdateMutation)

    const valid = (step === 0) ? user?.type : (user?.firstName && user?.lastName && user?.email)

    useEffect(() => {
        setEdit(true)

        if (params.id !== 'add') {
            // user exists - fetch data
            getUser({
                variables: {
                    id: params.id
                }
            }).then(response => {
                setStep(1)
                setupUser(response.data.user)
            }).catch(error => {
                window.alert(error.message)
            })
        }
    }, [])

    function setupUser(user) {
        const data = { ...user }
        const email = user?.emails?.find(o => o.label === 'main')
        const phone = user?.phones?.find(o => o.label === 'main')
        const gender = user?.latestData?.find(o => o.key === 'person.gender')
        const orgRole = user?.roles.find(o => o.org.id === org.id)

        if (email) data.email = email.address
        if (phone) data.phone = phone.number
        if (gender) data.gender = gender.values[0]
        if (orgRole) {
            data.type = orgRole.role

            if (orgRole.role === 'provider') {
                const daySchedules = user?.preferences.find(o => o.key === 'settings.day_schedules')

                if (daySchedules) {
                    const arr = JSON.parse(daySchedules.value[0])

                    data.daySchedules = arr.map(o => ({
                        ...o,
                        startTime: moment(o.startTime),
                        endTime: moment(o.endTime),
                    }))
                }
            }
        }

        setUser(data)
    }

    function cancel() {
        setEdit(false)
        navigate('/dashboard/setup/roster')
    }

    function handleChange(key, value) {
        setUser({
            ...user,
            [key]: value
        })
    }

    function handleStep(forward) {
        const nextStep = (forward && valid) ? (step + 1) : (step - 1)

        setStep(nextStep)
    }

    function save() {
        setRequesting(true)

        if (user?.id) {
            updateUser()
        } else {
            findOrCreateUser()
        }
    }

    function findOrCreateUser() {
        const { dob, firstName, lastName } = user

        userFindOrCreate({
            variables: {
                input: {
                    firstName,
                    lastName,
                    dob
                }
            }
        }).then(response => {
            const { errors, user } = response.data.userFindOrCreate

            if (user) {
                updateUser(user)
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            setRequesting(false)
            window.alert(error.message)
        })
    }

    function updateUser(newUser) {
        const { id, daySchedules, dob, file, firstName, lastName, email, phone, gender, type } = user
        const data = {
            id: newUser ? newUser.id : id,
            dob,
            firstName,
            lastName,
            gender
        }

        if (email) data.emails = [{ address: email }]
        if (phone) data.phones = [{ number: phone }]

        // setup roles
        if (type) {
            const newRole = { role: type, orgId: org.id }
            // new user, retain existing roles, otherwise override existing role with new selection
            const existingRoles = newUser ? newUser.roles : user.roles.filter(r => r.org.id !== org.id)

            data.roles = [...existingRoles.map(r => ({ role: r.role, orgId: r.org.id })), newRole]
        }

        if (file) data.attachments = [{ label: 'profile_picture', base64: file }]

        // setup default provider schedule
        if (daySchedules) {
            const schedules = daySchedules.filter(d => !d.remove).map(daySchedule => {
                const { concurrency, day, dayStr, startTime, endTime } = daySchedule

                return {
                    concurrency,
                    startTime,
                    endTime,
                    onlineStartTime: startTime,
                    onlineEndTime: endTime,
                    day,
                    dayStr
                }
            })

            data.preferences = [{ key: 'settings.day_schedules', value: JSON.stringify(schedules) }]
        }

        userUpdate({
            variables: {
                orgId: org.id,
                input: data
            }
        }).then(response => {
            const { errors, user } = response.data.userUpdate

            setRequesting(false)

            if (user) {
                setEdit(false)
                navigate('/dashboard/setup/roster')
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            setRequesting(false)
            window.alert(error.message)
        })
    }

    const Component = steps[step]   // dynamic step component

    if (loading) return <LoadingPane />

    return (
        <div className='setup-roster-user-view'>
            {requesting && <LoadingPane style={{ position: 'fixed' }} />}

            {edit &&
                <div className='floating-button-frame'>
                    <IconButton
                        onClick={cancel}
                        title={{
                            color: '#747A7A',
                            label: 'Cancel'
                        }}
                    />
                </div>
            }

            <div className='step-label'>{`Step ${step + 1} of ${steps.length}`}</div>

            <Component
                constants={constants}
                handleChange={handleChange}
                handleStep={handleStep}
                save={save}
                user={user}
                valid={valid}
            />
        </div>
    )
}

const Step1 = ({ constants, handleChange, handleStep, user, valid }) => {
    const { staffRoles } = constants

    return (
        <div className='step-content-view'>
            <div className='sections-frame scroll'>
                <div className='section-frame'>
                    <div className='section-header'>Select Staff Member Type</div>

                    <SelectMenu
                        className='staff-type-menu'
                        options={staffRoles}
                        onChange={value => handleChange('type', value)}
                        placeholder='Select staff member type'
                        value={user?.type}
                    />
                </div>
            </div>

            <div className='bottom-toolbar'>
                <IconButton
                    className={valid ? '' : 'disabled'}
                    iconAlign='right'
                    icon={require('../../../theme/assets/arrow-icon-white.png')}
                    iconHeight={18}
                    background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                    onClick={() => { if (valid) handleStep(true) }}
                    title={{
                        color: 'white',
                        label: 'Continue'
                    }}
                />
            </div>
        </div>
    )
}

const Step2 = ({ constants, handleChange, handleStep, save, user, valid }) => {
    const { genders } = constants
    const isProvider = (user.type === 'provider')

    return (
        <div className='step-content-view'>
            <div className='sections-frame scroll'>
                <div className='section-frame'>
                    <div className='section-header'>Member Details</div>

                    <div>
                        <ImageUpload
                            onChange={file => handleChange('file', file)}
                            user={user}
                        />

                        <div className='form-frame'>
                            <FloatingTextField
                                label='First name'
                                onChange={text => handleChange('firstName', text)}
                                value={user?.firstName}
                            />

                            <FloatingTextField
                                label='Last name'
                                onChange={text => handleChange('lastName', text)}
                                value={user?.lastName}
                            />

                            <DateTextField
                                label='Date of Birth'
                                placeholder='Select date of birth'
                                onChange={d => handleChange('dob', moment(d).format('YYYY-MM-DD'))}
                                value={user?.dob}
                            />

                            <FloatingTextField
                                label='Email address'
                                onChange={text => handleChange('email', text)}
                                value={user?.email}
                            />

                            <FloatingTextField
                                label='Phone'
                                onChange={text => handleChange('phone', text)}
                                type='tel'
                                value={user?.phone}
                            />

                            <SelectMenu
                                label='Gender'
                                options={genders}
                                onChange={value => handleChange('gender', value)}
                                placeholder='Select Gender'
                                value={user?.gender}
                            />
                        </div>
                    </div>
                </div>

                {isProvider &&
                    <>
                        <div className='section-frame'>
                            <div className='section-header'>Provider Schedule</div>

                            <div className='hint-frame'>
                                <div className='hint-label'>Add the days and time windows this provider works.</div>
                            </div>

                            <DaySchedules
                                daySchedules={user?.daySchedules}
                                onChange={daySchedules => handleChange('daySchedules', daySchedules)}
                            />
                        </div>

                        <div className='section-frame'>
                            <div className='section-header'>Vacation / Out of Office Days</div>

                            <div className='hint-frame'>
                                <div className='hint-label'>Add the dates this provider will not be available for appointments.</div>
                                <button className='hint-button'>+ Add Date Range</button>
                            </div>
                        </div>
                    </>
                }
            </div>

            <div className='bottom-toolbar' style={{ justifyContent: 'space-between' }}>
                <IconButton
                    icon={require('../../../theme/assets/arrow-icon-black.png')}
                    iconHeight={18}
                    onClick={() => handleStep(false)}
                    title={{
                        color: '#262D2D',
                        label: 'Previous'
                    }}
                />

                <IconButton
                    className={valid ? '' : 'disabled'}
                    icon={require('../../../theme/assets/check-icon-white.png')}
                    iconHeight={9.17}
                    background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                    onClick={() => { if (valid) save() }}
                    title={{
                        color: 'white',
                        label: 'Save'
                    }}
                />
            </div>
        </div>
    )
}

export default SetupRosterUser