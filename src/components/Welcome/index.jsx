import { useContext } from "react"
import { useNavigate } from "react-router"

import <PERSON><PERSON> from 'js-cookie'

import { UserContext } from '../../context/User'
import { selectedRole } from '../../helpers/Cookie'

import Breadcrumb from "../Shared/Breadcrumb"

const Welcome = () => {
    const navigate = useNavigate()
    const user = useContext(UserContext)

    function handlePlatform(platform) {
        Cookie.set(`${process.env.REACT_APP_COOKIE_NAME}-platform`, platform)

        if (!selectedRole) {
            const providerRole = user.roles.find(o => o.role === 'provider')

            // default role if none has been selected
            Cookie.set(`${process.env.REACT_APP_COOKIE_NAME}-role`, JSON.stringify(providerRole))

            navigate('/dashboard')
            window.location.reload()
        } else {
            navigate('/dashboard')
        }
    }

    return (
        <div className='welcome-view full-screen-view'>
            <div className='toolbar'>
                <Breadcrumb
                    buttons={[
                        { title: 'Sign In' },
                        { title: 'Welcome' }
                    ]}
                />
            </div>

            <div className='content-frame'>
                <div className='message'>Please select which wellup platform you would like to enter into.</div>

                <div className='submessage'>You can change between the two platforms at any time by clicking the icon in the upper left corner.</div>

                <div className='buttons-frame'>
                    <div className='button-frame orange'>
                        <img
                            alt='watermark-icon'
                            className='watermark-icon'
                            src={require('../../theme/assets/wellup-watermark-orange.png')}
                        />

                        <div className='title'>Wellup Social</div>

                        <div className='members'>1203 members</div>

                        <img
                            alt='arrow-icon'
                            className='arrow-icon'
                            src={require('../../theme/assets/arrow-icon-orange.png')}
                            onClick={() => handlePlatform('social')}
                        />
                    </div>

                    <div className='button-frame teal'>
                        <img
                            alt='watermark-icon'
                            className='watermark-icon'
                            src={require('../../theme/assets/wellup-watermark-teal.png')}
                        />

                        <div className='title'>Wellup Scheduler</div>

                        <div className='members'></div>

                        <img
                            alt='arrow-icon'
                            className='arrow-icon'
                            src={require('../../theme/assets/arrow-icon-teal.png')}
                            onClick={() => handlePlatform('scheduler')}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Welcome