import moment from "moment"

export function generateOptions(min, max, step) {
    let arr = []

    while (min <= max) {
        arr.push({ id: step ? (min * step) : min, value: step ? (min * step) : min })

        min++
    }

    return arr
}

export function generateDaysOfWeek(date) {
    const arr = []
    let i = 0

    while (i < 7) {
        const __d = date ? moment(date) : moment()
        const d = __d.startOf('week').add(i, 'day')

        arr.push({
            date: d,
            title: d.format('ddd - MMM DD'),
            selected: (moment().weekday() === i)
        })

        i++
    }

    return arr
}