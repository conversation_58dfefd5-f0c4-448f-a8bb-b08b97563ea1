import { useMutation } from "@apollo/client"

import ApptCancelMutation from "../graphql/mutations/Appointment/Cancel"
import ApptCompleteMutation from "../graphql/mutations/Appointment/Complete"
import ApptCheckInMutation from "../graphql/mutations/Appointment/CheckIn"
import ApptLeftMutation from "../graphql/mutations/Appointment/Left"
import ApptNoShowMutation from "../graphql/mutations/Appointment/NoShow"
import ApptStartMutation from "../graphql/mutations/Appointment/Start"
import ApptUndoStateMutation from "../graphql/mutations/Appointment/UndoState"

import ServiceQueueCancelMutation from "../graphql/mutations/Service/Queue/Cancel"
import ServiceQueueCheckInMutation from "../graphql/mutations/Service/Queue/CheckIn"
import ServiceQueueLeftMutation from "../graphql/mutations/Service/Queue/Left"
import ServiceQueueIgnoreMutation from "../graphql/mutations/Service/Queue/Ignore"
import ServiceQueueNoShowMutation from "../graphql/mutations/Service/Queue/NoShow"
import ServiceQueueStartMutation from "../graphql/mutations/Service/Queue/Start"
import ServiceQueueUndoStateMutation from "../graphql/mutations/Service/Queue/UndoState"

const useApptStatus = () => {
    const [apptCancel] = useMutation(ApptCancelMutation)
    const [apptComplete] = useMutation(ApptCompleteMutation)
    const [apptCheckIn] = useMutation(ApptCheckInMutation)
    const [apptLeft] = useMutation(ApptLeftMutation)
    const [apptNoShow] = useMutation(ApptNoShowMutation)
    const [apptStart] = useMutation(ApptStartMutation)
    const [apptUndo] = useMutation(ApptUndoStateMutation)

    const [queueCancel] = useMutation(ServiceQueueCancelMutation)
    const [queueCheckIn] = useMutation(ServiceQueueCheckInMutation)
    const [queueLeft] = useMutation(ServiceQueueLeftMutation)
    const [queueIgnore] = useMutation(ServiceQueueIgnoreMutation)
    const [queueNoShow] = useMutation(ServiceQueueNoShowMutation)
    const [queueStart] = useMutation(ServiceQueueStartMutation)
    const [queueUndo] = useMutation(ServiceQueueUndoStateMutation)

    return {
        apptCancel,
        apptComplete,
        apptCheckIn,
        apptLeft,
        apptNoShow,
        apptStart,
        apptUndo,

        queueCancel,
        queueCheckIn,
        queueLeft,
        queueIgnore,
        queueNoShow,
        queueStart,
        queueUndo
    }
}

export default useApptStatus