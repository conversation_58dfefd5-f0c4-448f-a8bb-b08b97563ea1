import { useState } from "react"

const useConstants = () => {
    const apptKinds = {
        appointment: {
            title: 'In-Facility',
            color: '#262D2D'
        },
        queue: {
            title: 'Queue',
            color: '#E97100'
        },
        'walk-in': {
            title: 'Walk-In',
            color: '#E97100'
        }
    }

    const apptStatuses = {
        arrived: {
            title: 'Checked In',
            color: '#E97100',
            desc: 'Member was checked in by staff.',
            showDuration: true
        },
        booked: {
            title: 'Booked',
            color: '#747A7A',
            desc: 'Appointment was booked by the member through the wellup application.'
        },
        cancel: {
            title: 'Cancelled',
            color: 'red'
        },
        cancelled: {
            title: 'Cancelled',
            color: 'red',
            desc: 'Member cancelled their appointment.'
        },
        complete: {
            title: 'Complete',
            color: '#0CA44C'
        },
        completed: {
            title: 'Complete',
            color: '#0CA44C',
            desc: 'Appointment was marked complete.',
            showDuration: true
        },
        check_in: {
            title: 'Check In',
            color: '#E97100'
        },
        left: {
            title: 'Left',
            color: 'red',
            desc: 'Member left the facility.'
        },
        ignore: {
            title: 'Ignore',
            color: 'red',
        },
        ignored: {
            title: 'Ignored',
            color: 'red',
        },
        no_show: {
            title: 'No Show',
            color: 'red',
            desc: 'Member did not show up or check into the facility.'
        },
        pending: {
            title: 'Pending',
            color: 'grey'
        },
        ready: {
            title: 'Ready',
            color: '#529FF9',
            desc: 'Member information was verified and ready to begin visit.',
            showDuration: true
        },
        roomed: {
            title: 'In Progress',
            color: '#008390',
            showDuration: true
        },
        start: {
            title: 'Start',
            color: '#008390'
        }
    }

    const genders = [
        { id: 'male', value: 'Male' },
        { id: 'female', value: 'Female' },
        { id: 'non_binary', value: 'Non-Binary' },
        { id: 'trans', value: 'Trans' }
    ]

    const insurances = [
        { id: 0, carrier: 'Uninsured Members', type: '' },
        { id: 1, carrier: 'Aetna', type: 'PPO' },
        { id: 2, carrier: 'AvMed', type: 'HMO' },
        { id: 3, carrier: 'Humana', type: 'PPO' },
        { id: 4, carrier: 'United Healthcare', type: 'PPO' }
    ]

    const orgKinds = [
        { id: 'community_partner', value: 'Community Partner' },
        { id: 'facility', value: 'Facility' },
    ]

    const serviceKinds = [
        { id: 'appointment', value: 'Appointment' },
        // { id: 'provider_appt', value: 'Provider Appointment' },
        { id: 'queue', value: 'Queue' }
    ]

    const staffRoles = [
        { id: 'admin', value: 'Admin' },
        { id: 'provider', value: 'Provider' },
        { id: 'staff', value: 'Staff' }
    ]

    const usaStates = [
        { id: 'AL', value: 'Alabama' },
        { id: 'AK', value: 'Alaska' },
        { id: 'AZ', value: 'Arizona' },
        { id: 'AR', value: 'Arkansas' },
        { id: 'CA', value: 'California' },
        { id: 'CO', value: 'Colorado' },
        { id: 'CT', value: 'Connecticut' },
        { id: 'DE', value: 'Delaware' },
        { id: 'FL', value: 'Florida' },
        { id: 'GA', value: 'Georgia' },
        { id: 'HI', value: 'Hawaii' },
        { id: 'ID', value: 'Idaho' },
        { id: 'IL', value: 'Illinois' },
        { id: 'IN', value: 'Indiana' },
        { id: 'IA', value: 'Iowa' },
        { id: 'KS', value: 'Kansas' },
        { id: 'KY', value: 'Kentucky' },
        { id: 'LA', value: 'Louisiana' },
        { id: 'ME', value: 'Maine' },
        { id: 'MD', value: 'Maryland' },
        { id: 'MA', value: 'Massachusetts' },
        { id: 'MI', value: 'Michigan' },
        { id: 'MN', value: 'Minnesota' },
        { id: 'MS', value: 'Mississippi' },
        { id: 'MO', value: 'Missouri' },
        { id: 'MT', value: 'Montana' },
        { id: 'NE', value: 'Nebraska' },
        { id: 'NV', value: 'Nevada' },
        { id: 'NH', value: 'New Hampshire' },
        { id: 'NJ', value: 'New Jersey' },
        { id: 'NM', value: 'New Mexico' },
        { id: 'NY', value: 'New York' },
        { id: 'NC', value: 'North Carolina' },
        { id: 'ND', value: 'North Dakota' },
        { id: 'OH', value: 'Ohio' },
        { id: 'OK', value: 'Oklahoma' },
        { id: 'OR', value: 'Oregon' },
        { id: 'PA', value: 'Pennsylvania' },
        { id: 'RI', value: 'Rhode Island' },
        { id: 'SC', value: 'South Carolina' },
        { id: 'SD', value: 'South Dakota' },
        { id: 'TN', value: 'Tennessee' },
        { id: 'TX', value: 'Texas' },
        { id: 'UT', value: 'Utah' },
        { id: 'VT', value: 'Vermont' },
        { id: 'VA', value: 'Virginia' },
        { id: 'WA', value: 'Washington' },
        { id: 'WV', value: 'West Virginia' },
        { id: 'WI', value: 'Wisconsin' },
        { id: 'WY', value: 'Wyoming' },
    ];

    return {
        apptKinds, apptStatuses,
        genders,
        insurances,
        orgKinds,
        serviceKinds,
        staffRoles,
        usaStates
    }
}

export default useConstants