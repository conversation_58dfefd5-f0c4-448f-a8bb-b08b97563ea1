import { ApolloClient, InMemoryCache } from '@apollo/client'
import { token } from '../helpers/<PERSON>ie'

const client = new ApolloClient({
    uri: process.env.REACT_APP_GRAPHQL_URL,
    cache: new InMemoryCache(),
    headers: token ? { Authorization: token } : {},
    defaultOptions: {
        watchQuery: {
            fetchPolicy: 'no-cache',
            errorPolicy: 'ignore',
        },
        query: {
            fetchPolicy: 'no-cache',
            errorPolicy: 'all',
        }
    }
});

export default client