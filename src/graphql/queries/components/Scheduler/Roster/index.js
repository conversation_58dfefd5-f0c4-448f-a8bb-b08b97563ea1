import { gql } from "@apollo/client"
import { USER_FRAGMENT } from "../../../../fragments/User"

const SchedulerRosterQuery = gql`
    query SchedulerRosterQuery($id: ID, $role: String) {
        org(id: $id) {
            id
            users(role: $role) {
                nodes {
                    ...UserFragment
                }
            }
        }
    }
    ${USER_FRAGMENT}
`

export default SchedulerRosterQuery