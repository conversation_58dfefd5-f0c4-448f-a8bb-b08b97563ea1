import { gql } from "@apollo/client"

const AppointmentProviderTimesQuery = gql`
    query AppointmentProviderTimesQuery($org: ID, $service: ID, $provider: ID, $date: String) {
        org(id: $org) {
            id
            service(id: $service) {
                id
                title
                availableTimesIso(date: $date, providerId: $provider)
            }
        }
    }
`

export default AppointmentProviderTimesQuery