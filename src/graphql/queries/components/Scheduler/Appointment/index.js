import { gql } from "@apollo/client"
import { APPOINTMENT_FRAGMENT } from '../../../../fragments/Appointment'
import { USER_FRAGMENT } from '../../../../fragments/User'

const AppointmentQuery = gql`
    query AppointmentQuery($id: ID, $appt: ID) {
        org(id: $id) {
            id
            appointment(id: $appt) {
                ...AppointmentFragment
                statusChanges {
                    id
                    createdAtIso
                    fromState
                    toState
                    undoneByUser {
                        ...UserFragment
                    }
                    user {
                        ...UserFragment
                    }
                    updatedAtIso
                }
            }
        }
    }
    ${APPOINTMENT_FRAGMENT}
`

export default AppointmentQuery