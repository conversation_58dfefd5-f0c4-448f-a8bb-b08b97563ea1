import { gql } from "@apollo/client"
import { USER_FRAGMENT } from '../../../../fragments/User'

const AppointmentCreateQuery = gql`
    query AppointmentCreateQuery($id: ID) {
        org(id: $id) {
            id
            services {
                id
                durationMins
                kind
                status
                title
            }
            users(role: "person") {
                nodes {
                    ...UserFragment
                }
            }
            providers {
                nodes {
                    id
                    firstName
                    lastName
                }
            }
        }
    }
    ${USER_FRAGMENT}
`

export default AppointmentCreateQuery