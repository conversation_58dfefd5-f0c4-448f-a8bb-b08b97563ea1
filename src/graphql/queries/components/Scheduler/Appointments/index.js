import { gql } from "@apollo/client"
import { APPOINTMENT_FRAGMENT } from '../../../../fragments/Appointment'
import { SERVICE_QUEUE_ENTRY_FRAGMENT } from '../../../../fragments/ServiceQueue'

const AppointmentsQuery = gql`
    query AppointmentsQuery($id: ID, $date: String, $status: [String!]) {
        org(id: $id) {
            id
            services {
                id
                appointments(date: $date) {
                    nodes {
                        ...AppointmentFragment
                    }
                }
                durationMins
                kind
                queue(date: $date, status: $status) {
                    nodes {
                        ...ServiceQueueEntryFragment
                    }
                }
                status
                title
            }
            providerCount
        }
    }
    ${APPOINTMENT_FRAGMENT}
    ${SERVICE_QUEUE_ENTRY_FRAGMENT}
`

export default AppointmentsQuery