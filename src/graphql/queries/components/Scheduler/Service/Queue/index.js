import { gql } from "@apollo/client"
import { USER_FRAGMENT } from "../../../../../fragments/User"

const ServiceQueueEntryQuery = gql`
    query ServiceQueueEntryQuery($id: ID, $service: ID, $queue: ID) {
        org(id: $id) {
            id
            service(id: $service) {
                id
                queueEntry(id: $queue) {
                    id
                    availableActions
                    beginAtIso
                    endAtIso
                    kind
                    isLate
                    lateMins
                    lastChangeAtIso
                    periodDurationMins
                    person {
                        ...UserFragment
                    }
                    scheduledAtIso
                    service {
                        id
                        title
                    }
                    status
                    statusChanges {
                    id
                    createdAtIso
                    fromState
                    toState
                    undoneByUser {
                        ...UserFragment
                    }
                    user {
                        ...UserFragment
                    }
                    updatedAtIso
                    }
                }
            }
        }
    }
    ${USER_FRAGMENT}
`

export default ServiceQueueEntryQuery