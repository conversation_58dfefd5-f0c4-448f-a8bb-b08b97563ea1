import { gql } from "@apollo/client"
import { USER_FRAGMENT } from '../../../../../fragments/User'

const ServiceQueueCreateQuery = gql`
    query ServiceQueueCreateQuery($id: ID) {
        org(id: $id) {
            id
            services {
                id
                kind
                nextAvailableAppt {
                    id
                    startIso
                }
                status
                title
            }
            users(role: "person") {
                nodes {
                    ...UserFragment
                }
            }
        }
    }
    ${USER_FRAGMENT}
`

export default ServiceQueueCreateQuery