import { gql } from '@apollo/client'

const SessionQuery = gql`
    query SessionQuery {
        me {
            id
            attachments(labels: ["profile_picture"]) {
                id
                label
                contentUrl
            }
            firstName
            lastName
            roles {
                id
                role
                org {
                    id
                    name
                }
            }
            preferences {
                id
                key
                value
            }
        }
    }
`

export default SessionQuery