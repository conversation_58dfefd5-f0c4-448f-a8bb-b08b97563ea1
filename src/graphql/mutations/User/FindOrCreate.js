import { gql } from '@apollo/client'
import { USER_FRAGMENT } from '../../fragments/User'

const UserFindOrCreateMutation = gql`
    mutation UserFindOrCreateMutation($input: UserFindOrCreateInput) {
        userFindOrCreate(input: $input) {
            errors {
                message
            }
            success
            user {
                ...UserFragment
            }
        }
    }
    ${USER_FRAGMENT}
`

export default UserFindOrCreateMutation