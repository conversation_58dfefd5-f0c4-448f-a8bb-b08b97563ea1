import { gql } from '@apollo/client'
import { USER_FRAGMENT } from '../../fragments/User'

const UserUpdateMutation = gql`
    mutation UserUpdateMutation($orgId: ID, $input: UserUpdateInput!) {
        userUpdate(orgId: $orgId, input: $input) {
            errors {
                message
            }
            success
            user {
                ...UserFragment
            }
        }
    }
    ${USER_FRAGMENT}
`

export default UserUpdateMutation