import { gql } from '@apollo/client'
import { APPOINTMENT_FRAGMENT } from '../../fragments/Appointment'

const ApptNoShowMutation = gql`
    mutation ApptNoShowMutation($input: IDInput) {
        apptNoShow(input: $input) {
            errors {
                message
            }
            success
            appointment {
                ...AppointmentFragment
            }
        }
    }
    ${APPOINTMENT_FRAGMENT}
`

export default ApptNoShowMutation