import { gql } from '@apollo/client'
import { APPOINTMENT_FRAGMENT } from '../../fragments/Appointment'

const ApptCheckInMutation = gql`
    mutation ApptCheckInMutation($input: IDInput) {
        apptCheckIn(input: $input) {
            errors {
                message
            }
            success
            appointment {
                ...AppointmentFragment
            }
        }
    }
    ${APPOINTMENT_FRAGMENT}
`

export default ApptCheckInMutation