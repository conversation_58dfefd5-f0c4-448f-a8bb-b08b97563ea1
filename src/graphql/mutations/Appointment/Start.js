import { gql } from '@apollo/client'
import { APPOINTMENT_FRAGMENT } from '../../fragments/Appointment'

const ApptStartMutation = gql`
    mutation ApptStartMutation($input: IDInput) {
        apptStart(input: $input) {
            errors {
                message
            }
            success
            appointment {
                ...AppointmentFragment
            }
        }
    }
    ${APPOINTMENT_FRAGMENT}
`

export default ApptStartMutation