import { gql } from '@apollo/client'
import { APPOINTMENT_FRAGMENT } from '../../fragments/Appointment'

const ApptLeftMutation = gql`
    mutation ApptLeftMutation($input: IDInput) {
        apptLeft(input: $input) {
            errors {
                message
            }
            success
            appointment {
                ...AppointmentFragment
            }
        }
    }
    ${APPOINTMENT_FRAGMENT}
`

export default ApptLeftMutation