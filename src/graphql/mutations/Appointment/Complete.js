import { gql } from '@apollo/client'
import { APPOINTMENT_FRAGMENT } from '../../fragments/Appointment'

const ApptCompleteMutation = gql`
    mutation ApptCompleteMutation($input: IDInput) {
        apptComplete(input: $input) {
            errors {
                message
            }
            success
            appointment {
                ...AppointmentFragment
            }
        }
    }
    ${APPOINTMENT_FRAGMENT}
`

export default ApptCompleteMutation