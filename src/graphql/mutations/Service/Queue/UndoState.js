import { gql } from '@apollo/client'
import { SERVICE_QUEUE_ENTRY_FRAGMENT } from '../../../fragments/ServiceQueue'

const ServiceQueueUndoStateMutation = gql`
    mutation ServiceQueueUndoStateMutation($input: IDInput!) {
        serviceQueueUndoState(input: $input) {
            errors {
                message
            }
            queueEntry {
                ...ServiceQueueEntryFragment
            }
            success
        }
    }
    ${SERVICE_QUEUE_ENTRY_FRAGMENT}
`

export default ServiceQueueUndoStateMutation