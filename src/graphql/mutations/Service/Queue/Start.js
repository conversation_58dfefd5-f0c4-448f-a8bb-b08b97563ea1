import { gql } from '@apollo/client'
import { SERVICE_QUEUE_ENTRY_FRAGMENT } from '../../../fragments/ServiceQueue'

const ServiceQueueStartMutation = gql`
    mutation ServiceQueueStartMutation($input: IDInput!) {
        serviceQueueStart(input: $input) {
            errors {
                message
            }
            queueEntry {
                ...ServiceQueueEntryFragment
            }
            success
        }
    }
    ${SERVICE_QUEUE_ENTRY_FRAGMENT}
`

export default ServiceQueueStartMutation