import { gql } from '@apollo/client'
import { SERVICE_QUEUE_ENTRY_FRAGMENT } from '../../../fragments/ServiceQueue'

const ServiceQueueBookMutation = gql`
    mutation ServiceQueueBookMutation($input: IDInput!) {
        serviceQueueBook(input: $input) {
            errors {
                message
            }
            queueEntry {
                ...ServiceQueueEntryFragment
            }
            success
        }
    }
    ${SERVICE_QUEUE_ENTRY_FRAGMENT}
`

export default ServiceQueueBookMutation