import { gql } from '@apollo/client'
import { SERVICE_QUEUE_ENTRY_FRAGMENT } from '../../../fragments/ServiceQueue'

const ServiceQueueAddMutation = gql`
    mutation ServiceQueueAddMutation($input: ServiceQueueEntryCreateInput!) {
        serviceQueueAdd(input: $input) {
            errors {
                message
            }
            queueEntry {
                ...ServiceQueueEntryFragment
            }
            success
        }
    }
    ${SERVICE_QUEUE_ENTRY_FRAGMENT}
`

export default ServiceQueueAddMutation