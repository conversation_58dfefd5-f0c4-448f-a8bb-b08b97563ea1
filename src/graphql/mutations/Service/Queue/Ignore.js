import { gql } from '@apollo/client'
import { SERVICE_QUEUE_ENTRY_FRAGMENT } from '../../../fragments/ServiceQueue'

const ServiceQueueIgnoreMutation = gql`
    mutation ServiceQueueIgnoreMutation($input: IDInput!) {
        serviceQueueIgnore(input: $input) {
            errors {
                message
            }
            queueEntry {
                ...ServiceQueueEntryFragment
            }
            success
        }
    }
    ${SERVICE_QUEUE_ENTRY_FRAGMENT}
`

export default ServiceQueueIgnoreMutation