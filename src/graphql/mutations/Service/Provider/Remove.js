
import { gql } from '@apollo/client'

const ServiceProviderRemoveMutation = gql`
    mutation ServiceProviderRemoveMutation($input: ServiceProviderInput) {
        serviceProviderRemove(input: $input) {
            errors {
                message
            }
            service {
                id
                status
            }
            success
        }
    }
`

export default ServiceProviderRemoveMutation