
import { gql } from '@apollo/client'

const ServiceProviderAddMutation = gql`
    mutation ServiceProviderAddMutation($input: ServiceProviderInput) {
        serviceProviderAdd(input: $input) {
            errors {
                message
            }
            service {
                id
                status
            }
            success
        }
    }
`

export default ServiceProviderAddMutation