import { gql } from '@apollo/client'
import { SERVICE_FRAGMENT } from '../../fragments/Service'

const ServiceUpdateMutation = gql`
    mutation ServiceUpdateMutation($input: ServiceUpdateInput!) {
        serviceUpdate(input: $input) {
            errors {
                message
            }
            success
            service {
                ...ServiceFragment
            }
        }
    }
    ${SERVICE_FRAGMENT}
`

export default ServiceUpdateMutation