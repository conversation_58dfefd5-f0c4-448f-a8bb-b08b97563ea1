import { gql } from '@apollo/client'
import { SERVICE_FRAGMENT } from '../../fragments/Service'

const ServiceCreateMutation = gql`
    mutation ServiceCreateMutation($input: ServiceCreateInput!) {
        serviceCreate(input: $input) {
            errors {
                message
            }
            success
            service {
                ...ServiceFragment
            }
        }
    }
    ${SERVICE_FRAGMENT}
`

export default ServiceCreateMutation