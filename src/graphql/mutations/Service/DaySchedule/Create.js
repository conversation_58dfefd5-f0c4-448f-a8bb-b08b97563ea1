import { gql } from '@apollo/client'

const ServiceDayScheduleCreateMutation = gql`
    mutation ServiceDayScheduleCreateMutation($input: ServiceDayScheduleCreateInput) {
        serviceDayScheduleCreate(input: $input) {
            errors {
                message
            }
            daySchedule {
                id
                concurrency
                dayName
            }
        }
    }
`

export default ServiceDayScheduleCreateMutation