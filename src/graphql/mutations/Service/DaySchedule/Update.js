import { gql } from '@apollo/client'

const ServiceDayScheduleUpdateMutation = gql`
    mutation ServiceDayScheduleUpdateMutation($input: ServiceDayScheduleUpdateInput) {
        serviceDayScheduleUpdate(input: $input) {
            errors {
                message
            }
            daySchedule {
                id
                concurrency
            }
        }
    }
`

export default ServiceDayScheduleUpdateMutation