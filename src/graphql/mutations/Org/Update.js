import { gql } from '@apollo/client'
import { ORGANIZATION_FRAGMENT } from '../../fragments/Organization'

const OrgUpdateMutation = gql`
    mutation OrgUpdateMutation($input: OrganizationUpdateInput!) {
        orgUpdate(input: $input) {
            errors {
                message
            }
            success
            organization {
                ...OrganizationFragment
            }
        }
    }
    ${ORGANIZATION_FRAGMENT}
`

export default OrgUpdateMutation