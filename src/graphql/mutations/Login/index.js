import { gql } from '@apollo/client'

const LoginMutation = gql`
    mutation LoginMutation($input: UserLoginInput!) {
        login(input: $input) {
            errors {
                message
            }
            success
            user {
                id
                firstName
                lastName
                token
                roles {
                    id
                    role
                    org {
                        id
                    }
                }
            }
        }
    }
`

export default LoginMutation