import { gql } from '@apollo/client'

import { USER_FRAGMENT } from './User'

export const APPOINTMENT_FRAGMENT = gql`
    fragment AppointmentFragment on Appointment {
        id
        availableActions
        beginAtIso
        endAtIso
        kind
        isLate
        lateMins
        lastChangeAtIso
        periodDurationMins
        person {
            ...UserFragment
        }
        provider {
            ...UserFragment
        }
        scheduledAtIso
        service {
            id
            title
        }
        status
    }
    ${USER_FRAGMENT}
`