import { gql } from '@apollo/client'
import { USER_FRAGMENT } from './User'

export const SERVICE_QUEUE_ENTRY_FRAGMENT = gql`
    fragment ServiceQueueEntryFragment on ServiceQueueEntry {
        id
        availableActions
        beginAtIso
        elapsedMins
        endAtIso
        kind
        isLate
        lateMins
        lastChangeAtIso
        periodDurationMins
        person {
            ...UserFragment
        }
        service {
            id
            title
        }
        status
    }
        ${USER_FRAGMENT}
`