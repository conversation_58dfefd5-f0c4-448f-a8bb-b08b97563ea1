import { gql } from '@apollo/client'

export const USER_FRAGMENT = gql`
    fragment UserFragment on User {
        id
        attachments(labels: ["profile_picture"]) {
            id
            label
            contentUrl
        }
        dob
        firstName
        lastName
        emails {
            id
            label
            address
        }
        latestData(metrics: ["person.gender"]) {
            id
            key
            values
        }
        phones {
            id
            label
            number
        }
        preferences {
            id
            key
            value
        }
        roles {
            id
            role
            org {
                id
            }
        }
    }
`