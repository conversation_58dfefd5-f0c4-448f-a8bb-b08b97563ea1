{"name": "wellup-web", "version": "0.1.0", "engines": {"node": "20.x"}, "private": true, "dependencies": {"@apollo/client": "^3.12.7", "cra-template": "1.2.0", "graphql": "^16.10.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "prop-types": "^15.8.1", "react": "^18.0.0", "react-bootstrap": "^2.10.9", "react-datepicker": "^8.0.0", "react-dom": "^18.2.0", "react-router": "^7.1.3", "react-scripts": "5.0.1", "react-slider": "^2.0.6", "react-toastify": "^11.0.3", "sass": "^1.83.4", "serve": "^14.2.4", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "^9.19.0", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.4", "globals": "^15.14.0"}}